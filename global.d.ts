export {}

declare global {
  interface FileSystemObserverObserveOptions {
    recursive?: boolean
  }

  type FileSystemChangeType = 'appeared' | 'disappeared' | 'modified' | 'moved' | 'unknown' | 'errored'

  interface FileSystemChangeRecord {
    readonly changedHandle: FileSystemFileHandle
    readonly relativePathComponents: ReadonlyArray<string>
    readonly type: FileSystemChangeType
    readonly relativePathMovedFrom?: ReadonlyArray<string>
  }

  interface FileSystemObserver {
    observe(
      handle: FileSystemFileHandle | FileSystemDirectoryHandle,
      options?: FileSystemObserverObserveOptions,
    ): Promise<void>
    unobserve(handle: FileSystemFileHandle | FileSystemDirectoryHandle): void
    disconnect(): void
  }

  type FileSystemObserverCallback = (records: FileSystemChangeRecord[], observer: FileSystemObserver) => void

  declare const FileSystemObserver: {
    prototype: FileSystemObserver
    new (callback: FileSystemObserverCallback): FileSystemObserver
  }

  interface Window {
    FileSystemObserver: FileSystemObserver
  }
}
