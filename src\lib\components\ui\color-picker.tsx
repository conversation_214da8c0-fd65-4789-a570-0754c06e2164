'use client'

import { FC, useMemo } from 'react'
import { HexColorPicker } from 'react-colorful'
import { cn } from '@/lib/utils'
import { Button } from '@/lib/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/lib/components/ui/popover'
import { Input } from '@/lib/components/ui/input'

interface ColorPickerProps {
  value: string
  onChange?: (value: string) => void
  onValueCommit?: (value: string) => void
  onBlur?: () => void
  children?: React.ReactNode
}

const ColorPicker: FC<Omit<React.ComponentProps<'button'>, 'value' | 'onChange' | 'onBlur'> & ColorPickerProps> = ({
  disabled,
  value,
  onChange,
  onValueCommit,
  onBlur,
  name,
  className,
  children,
  ...props
}) => {
  const parsedValue = useMemo(() => {
    return value || '#FFFFFF'
  }, [value])

  return (
    <Popover
      onOpenChange={(open) => {
        if (!open) {
          onValueCommit?.(parsedValue)
        }
      }}
    >
      <PopoverTrigger asChild disabled={disabled} onBlur={onBlur}>
        {children ?? (
          <Button
            {...props}
            className={cn('block size-9', className)}
            name={name}
            style={{
              backgroundColor: parsedValue,
            }}
            variant="outline"
          ></Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-full space-y-4">
        <HexColorPicker color={parsedValue} onChange={onChange} />
        <Input
          maxLength={7}
          onChange={(e) => {
            onChange?.(e?.currentTarget?.value)
          }}
          className="w-50"
          value={parsedValue}
        />
      </PopoverContent>
    </Popover>
  )
}
ColorPicker.displayName = 'ColorPicker'

export { ColorPicker }
