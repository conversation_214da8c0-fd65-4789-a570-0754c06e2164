import { FC, ReactNode, use } from 'react'
import type { Metadata } from 'next'
import ViewAlbumClientLayout from './ViewAlbumClientLayout'

type Props = {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params

  return {
    title: `Album ${id}`,
  }
}

const ViewAlbumLayout: FC<{ children: ReactNode; params: Promise<{ id: string }> }> = ({ children, params }) => {
  const { id: albumId } = use(params)
  return <ViewAlbumClientLayout albumId={albumId}>{children}</ViewAlbumClientLayout>
}

export default ViewAlbumLayout
