'use client'

import { <PERSON> } from 'react'
import { Button } from './ui/button'
import { QrCode } from 'lucide-react'
import { useAtom } from 'jotai'
import { isQRCodeOpenAtom } from '../atoms'

const QRCodeButton: FC = () => {
  const [isQRCodeOpen, setIsQRCodeOpen] = useAtom(isQRCodeOpenAtom)

  return (
    <Button
      variant={isQRCodeOpen ? 'default' : 'secondary'}
      size="icon"
      className="rounded-full size-10"
      onClick={() => setIsQRCodeOpen(!isQRCodeOpen)}
    >
      <QrCode />
    </Button>
  )
}

export default QRCodeButton
