import { FC, useEffect, useMemo, useRef, useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/lib/components/ui/dropdown-menu'
import { Button } from '@/lib/components/ui/button'
import { ChevronDown, EllipsisVertical, Loader2, LoaderCircle, MessageSquareQuote, Trash2, X } from 'lucide-react'
import { Separator } from '@/lib/components/ui/separator'
import { ScrollArea, ScrollBar } from '@/lib/components/ui/scroll-area'
import { ThreadCommentItem, ThreadCommentLayout } from './ThreadComment'
import ImageCommentForm, { ImageCommentFormRef } from './ImageCommentForm'
import { useAtomValue } from 'jotai'
import { albumPasscodeAtom, userAtom } from '@/lib/atoms'
import { toast } from 'sonner'
import { CommentSchemaType } from '@/lib/zod-schema/comment-schema'
import { UseFormReturn } from 'react-hook-form'
import { axiosInstance } from '@/lib/axios'
import { PaginationResponse, Thread, ThreadComment } from '@/lib/types'
import { keepPreviousData, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { cn, uuidv4 } from '@/lib/utils'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/lib/components/ui/alert-dialog'

type ImageThreadDetailProps = {
  thread: Thread
  onClose?: () => void
}

const ImageThreadDetail: FC<ImageThreadDetailProps> = ({ thread, onClose }) => {
  const user = useAtomValue(userAtom)
  const queryClient = useQueryClient()
  const [isDeleteTagOpen, setIsDeleteTagOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const newCommentsRef = useRef<ThreadComment[]>([])
  const queryKey = ['image:thread', thread.uuid]
  const albumPasscode = useAtomValue(albumPasscodeAtom)
  const enteredPasscode = albumPasscode[thread.albumCode]
  const { data, hasNextPage, fetchNextPage, isFetching, isFetchingNextPage } = useInfiniteQuery<
    PaginationResponse<ThreadComment>
  >({
    queryKey: queryKey,
    retry: false,
    placeholderData: keepPreviousData,
    getNextPageParam: (lastPage) => (lastPage.page < lastPage.totalPage ? lastPage.page + 1 : undefined),
    getPreviousPageParam: (firstPage) => (firstPage.page > 1 ? firstPage.page - 1 : undefined),
    initialPageParam: 1,
    refetchOnMount: false,
    queryFn: async ({ signal, pageParam }) => {
      const searchParams = new URLSearchParams()
      searchParams.set('album_cid', thread.albumCode)
      searchParams.set('image_id', thread.imageId.toString())
      searchParams.set('page', String(pageParam))

      const res = await axiosInstance.get(
        `/customer/album/image/interactive/thread/comment/${thread.uuid}?${searchParams}`,
        {
          signal,
          headers: {
            passcode: enteredPasscode || undefined,
          },
        },
      )

      if (res.status === 204) {
        return {
          data: [],
          page: 1,
          totalPage: 1,
        }
      }

      res.data.comment_list?.forEach((comment: ThreadComment) => {
        comment.threadId = comment.uuid
        comment.imageId = comment.imageId
        comment.albumCode = comment.albumCode

        const index = newCommentsRef.current.findIndex((c) => c.uuid === comment.uuid)
        if (index >= 0) {
          newCommentsRef.current.splice(index, 1)
        }
      })

      return {
        data: (res.data.comment_list || []) as ThreadComment[],
        page: res.data.stats.current_page as number,
        totalPage: res.data.stats.total_page as number,
      }
    },
  })
  const comments = useMemo(() => data?.pages.flatMap((x) => x.data) || [], [data])
  const isFirstFetch = isFetching && !comments.length
  const wrapperRef = useRef<HTMLDivElement>(null)
  const newCommentLayoutRef = useRef<HTMLDivElement>(null)
  const newCommentFormRef = useRef<ImageCommentFormRef>(null)
  const [isShowCommentForm, setIsShowCommentForm] = useState(false)

  const { mutate } = useMutation({
    mutationFn: async (input: CommentSchemaType & { form: UseFormReturn<CommentSchemaType>; tmpId: string }) => {
      const res = await axiosInstance.post(
        `/customer/album/image/interactive/thread/comment/${thread.uuid}`,
        {
          image_id: thread.imageId,
          album_cid: thread.albumCode,
          content: input.content,
        },
        {
          headers: {
            passcode: enteredPasscode || undefined,
          },
        },
      )
      return res.data.data as ThreadComment
    },
    onMutate: async (input) => {
      await queryClient.cancelQueries({ queryKey: queryKey })
      newCommentsRef.current.push({
        uuid: input.tmpId,
        albumCode: thread.albumCode,
        imageId: thread.imageId,
        threadId: thread.uuid,
        content: input.content,
        created_at: new Date().toISOString(),
        last_updated: new Date().toISOString(),
        username: user?.username,
      })
    },
    onSuccess: (newComment, input) => {
      input.form.reset()

      const index = newCommentsRef.current.findIndex((c) => c.uuid === input.tmpId)
      if (index >= 0) {
        newCommentsRef.current[index] = newComment
      }

      queryClient.invalidateQueries({ queryKey: queryKey })
    },
    onError: (err, input) => {
      const index = newCommentsRef.current.findIndex((c) => c.uuid === input.tmpId)
      if (index >= 0) {
        newCommentsRef.current.splice(index, 1)
      }

      if (err instanceof AxiosError) {
        if (err?.code === 'ERR_NETWORK') {
          toast.error('Lỗi mạng, vui lòng thử lại sau')
        } else if (err.code === 'ERR_BAD_REQUEST') {
          toast.error(err.response?.data.error.message || 'Thông tin không hợp lệ')
        }
      } else {
        console.error(err)
        toast.error('Lỗi không xác định')
      }
    },
  })

  const handleDeleteThread = () => {
    setIsDeleting(true)
    toast.promise(
      axiosInstance
        .delete(`/customer/album/image/interactive/thread/${thread.uuid}`, {
          data: {
            image_id: thread.imageId,
            album_cid: thread.albumCode,
            passcode: enteredPasscode,
          },
        })
        .then(() => {
          setIsDeleteTagOpen(false)
          queryClient.invalidateQueries({ queryKey: ['image:threads', thread.imageId] })
          onClose?.()
          return 'Đã lưu thay đổi'
        })
        .catch((err) => {
          if (err instanceof AxiosError) {
            if (err?.code === 'ERR_NETWORK') {
              throw 'Lỗi mạng, vui lòng thử lại sau'
            } else if (err.code === 'ERR_BAD_REQUEST') {
              throw err.response?.data.error.message || 'Thông tin không hợp lệ'
            }
          } else {
            console.error(err)
            throw 'Lỗi không xác định'
          }
        })
        .finally(() => {
          setIsDeleting(false)
        }),
      {
        loading: 'Đang lưu thay đổi...',
        success: (msg) => msg ?? '',
        error: (msg) => msg,
      },
    )
  }

  const handleNewCommentSubmit = (values: CommentSchemaType, form: UseFormReturn<CommentSchemaType>) => {
    mutate({
      ...values,
      form,
      tmpId: uuidv4(),
    })
  }

  const focusOnCommentForm = () => {
    const scrollViewport = wrapperRef.current?.querySelector('[data-slot=scroll-area-viewport]')
    if (scrollViewport) {
      scrollViewport.scrollTop = scrollViewport.scrollHeight
    }
    if (newCommentFormRef.current) {
      newCommentFormRef.current.focus()
    }
  }

  useEffect(() => {
    const newCommentLayout = newCommentLayoutRef.current
    const observer = new IntersectionObserver((entries) => {
      setIsShowCommentForm(entries[0].isIntersecting)
    })
    const wrapper = wrapperRef.current
    const handleWheel = (e: WheelEvent) => {
      e.stopPropagation()
    }

    wrapper?.addEventListener('wheel', handleWheel, { passive: false })
    if (newCommentLayout) {
      observer.observe(newCommentLayout)
    }

    return () => {
      wrapper?.removeEventListener('wheel', handleWheel)
      observer.disconnect()
    }
  }, [])

  return (
    <>
      <div className="bg-background rounded-lg comment-detail shadow-lg" ref={wrapperRef}>
        <div className="h-9 flex justify-between items-center">
          <span className="ml-4 font-semibold text-xs">Comment</span>
          <div className="flex gap-1">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="size-7 !p-0 mr-1">
                  <EllipsisVertical className="size-4.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  variant="destructive"
                  className="cursor-pointer"
                  onMouseDown={(e) => {
                    e.stopPropagation()
                  }}
                  onClick={() => setIsDeleteTagOpen(true)}
                  disabled={isDeleteTagOpen}
                >
                  <Trash2 className="text-inherit" />
                  <span className="translate-y-0.5">Xoá</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="ghost" className="size-7 !p-0 mr-1" onClick={onClose}>
              <X className="size-4.5" />
            </Button>
          </div>
        </div>
        <Separator className="bg-accent" />
        <div className="relative">
          <ScrollArea className="h-fit max-h-96">
            <div className="px-4 pt-3 py-4 flex flex-col gap-4">
              {comments.map((comment) => (
                <ThreadCommentItem key={comment.uuid} username={comment.username ?? 'Khách'} comment={comment} />
              ))}
              {(isFirstFetch || isFetchingNextPage || hasNextPage) && (
                <div className="flex justify-center items-center h-8">
                  {(isFirstFetch || isFetchingNextPage) && <Loader2 className="animate-spin" />}
                  {!isFetchingNextPage && hasNextPage && (
                    <Button variant="ghost" size="sm" onClick={() => fetchNextPage()}>
                      <ChevronDown />
                    </Button>
                  )}
                </div>
              )}
              {newCommentsRef.current.map((comment) => (
                <ThreadCommentItem key={comment.uuid} username={comment.username ?? 'Khách'} comment={comment} />
              ))}
              <ThreadCommentLayout username={user?.username ?? 'Khách'} ref={newCommentLayoutRef}>
                <div className="pl-2">
                  <ImageCommentForm onSubmit={handleNewCommentSubmit} ref={newCommentFormRef} />
                </div>
              </ThreadCommentLayout>
            </div>
            <ScrollBar />
          </ScrollArea>
          <Button
            variant="secondary"
            className={cn('absolute right-4 bottom-4', { 'opacity-0 pointer-events-none': isShowCommentForm })}
            size="sm"
            onClick={focusOnCommentForm}
          >
            <MessageSquareQuote />
          </Button>
        </div>
      </div>
      <AlertDialog
        open={isDeleteTagOpen}
        onOpenChange={(open) => {
          if (!isDeleting) {
            setIsDeleteTagOpen(open)
          }
        }}
      >
        <AlertDialogContent className="sm:max-w-[425px]">
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận</AlertDialogTitle>
            <AlertDialogDescription>Bạn có chắc chắn muốn xoá thread này?</AlertDialogDescription>
          </AlertDialogHeader>
          {comments[0] && (
            <div className="flex justify-center items-center">
              <ThreadCommentItem
                key={comments[0].uuid}
                username={comments[0].username ?? 'Khách'}
                comment={comments[0]}
              />
            </div>
          )}

          <AlertDialogFooter>
            <AlertDialogCancel>Huỷ</AlertDialogCancel>
            <Button onClick={handleDeleteThread} disabled={isDeleting}>
              {isDeleting && <LoaderCircle className="animate-spin" />}
              Đồng ý
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default ImageThreadDetail
