'use client'

import { store, syncingImagesAtom } from '@/lib/atoms'
import Container from '@/lib/components/Container'
import EmptyImageList from '@/lib/components/EmptyImageList'
import QRCodeButton from '@/lib/components/QRCodeButton'
import { Button } from '@/lib/components/ui/button'
import { Checkbox } from '@/lib/components/ui/checkbox'
import { Label } from '@/lib/components/ui/label'
import UploadingImageGrid from '@/lib/components/UploadingImageGrid'
import { processImage } from '@/lib/image-process'
import { UploadingImage } from '@/lib/types'
import { cn, uuidv4 } from '@/lib/utils'
import { Icon } from '@iconify/react/dist/iconify.js'
import { useAtom } from 'jotai'
import { FolderSync } from 'lucide-react'
import { FC, use, useCallback, useState } from 'react'
import { toast } from 'sonner'

const AutoSyncPage: FC<{ params: Promise<{ id: string }> }> = ({ params }) => {
  const [syncingImages, setSyncingImages] = useAtom(syncingImagesAtom)
  const { id: albumId } = use(params)
  const [isDragOver, setIsDragOver] = useState(false)
  const [syncExistedImages, setSyncExistedImages] = useState(false)
  const editUploadingImage = useCallback(
    (id: string, dirId: string, data: Partial<UploadingImage>) => {
      setSyncingImages((prev) => {
        let uploadingImages = prev[albumId] ?? {}
        const images = uploadingImages[dirId]?.images ?? []
        const index = images.findIndex((x) => x.id === id)

        if (index != null && index > -1) {
          images[index] = {
            ...images[index],
            ...data,
          }
          uploadingImages[dirId] = { ...uploadingImages[dirId], images: [...images] }
          uploadingImages = { ...uploadingImages }
        }

        return { ...prev, [albumId]: uploadingImages }
      })
    },
    [setSyncingImages, albumId],
  )
  const removeUploadingImage = useCallback(
    (id: string, dirId: string) => {
      setSyncingImages((prev) => {
        let uploadingImages = prev[albumId] ?? {}
        const images = (uploadingImages[dirId]?.images ?? []).filter((x) => x.id !== id)

        uploadingImages[dirId] = { ...uploadingImages[dirId], images }
        uploadingImages = { ...uploadingImages }

        return { ...prev, [albumId]: uploadingImages }
      })
    },
    [setSyncingImages, albumId],
  )

  const handleFileSystemDirectories = async (dirHandles: FileSystemDirectoryHandle[]) => {
    for (const dirHandle of dirHandles) {
      const dirId = uuidv4()
      const images: UploadingImage[] = []
      const syncingImages = store.get(syncingImagesAtom)
      const dirNames = Object.values(syncingImages[albumId] ?? {}).map((x) => x.handle.name)

      if (dirNames.includes(dirHandle.name)) {
        toast.error(`Thư mục ${dirHandle.name} đã được đồng bộ trước đó`)
        continue
      }

      if (syncExistedImages) {
        for await (const handle of dirHandle.values()) {
          if (!(handle instanceof FileSystemFileHandle)) {
            continue
          }

          const file = await handle.getFile()

          if (!file.type.startsWith('image/')) {
            continue
          }

          const id = uuidv4()
          const fileName = file.name
          const controller = new AbortController()
          const process = processImage.bind(
            null,
            id,
            albumId,
            file,
            fileName,
            editUploadingImage.bind(null, id, dirId),
            controller,
          )

          images.push({
            id,
            file: file,
            fileName,
            state: 'pending',
            process,
            controller,
          })
        }
      }

      setSyncingImages((prev) => {
        prev[albumId] = prev[albumId] ?? {}
        prev[albumId][dirId] = { handle: dirHandle, images }
        return { ...prev }
      })

      let observer: FileSystemObserver | undefined

      if (window.FileSystemObserver) {
        observer = new FileSystemObserver(async (e) => {
          if (e[0].type === 'disappeared') {
            const syncingImages = store.get(syncingImagesAtom)
            const syncingImage = syncingImages[albumId]?.[dirId]?.images.find(
              (x) => x.fileName === e[0].relativePathComponents?.[0],
            )

            if (syncingImage && !['uploaded', 'uploading', 'deleted'].includes(syncingImage.state)) {
              syncingImage.controller.abort()
              editUploadingImage(syncingImage.id, dirId, { state: 'deleted', failedReason: 'Đã xóa' })
            }

            return
          }

          if (e[0].changedHandle.kind !== 'file') {
            return
          }

          const file = await e[0].changedHandle.getFile()

          if (file.size === 0 || !file.type.startsWith('image/') || file.name.endsWith('.tmp')) {
            return
          }

          if (e[0].type === 'modified') {
            const syncingImages = store.get(syncingImagesAtom)
            const syncingImage = syncingImages[albumId]?.[dirId]?.images.find((x) => x.fileName === file.name)

            if (syncingImage) {
              syncingImage.controller.abort()
              removeUploadingImage(syncingImage.id, dirId)
            }
          }

          if (e[0].type === 'moved') {
            const syncingImages = store.get(syncingImagesAtom)
            const syncingImage = syncingImages[albumId]?.[dirId]?.images.find(
              (x) => x.fileName === e[0].relativePathMovedFrom?.[0],
            )

            if (syncingImage) {
              syncingImage.controller.abort()
              editUploadingImage(syncingImage.id, dirId, {
                ...syncingImage,
                fileName: file.name,
                file,
              })
              return
            }
          }

          const id = uuidv4()
          const controller = new AbortController()
          const process = processImage.bind(
            null,
            id,
            albumId,
            file,
            file.name,
            editUploadingImage.bind(null, id, dirId),
            controller,
            50,
          )

          setSyncingImages((prev) => {
            prev[albumId] = prev[albumId] ?? {}
            prev[albumId][dirId] = {
              handle: dirHandle,
              images: [
                {
                  id,
                  file,
                  fileName: file.name,
                  state: 'pending',
                  process,
                  controller,
                },
                ...(prev[albumId][dirId]?.images ?? []),
              ],
            }
            return { ...prev }
          })

          process()
        })
        observer.observe(dirHandle)
      }

      images.forEach((x) => {
        x.process()
      })
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)

    const fileHandlesPromises = [...e.dataTransfer.items]
      .filter((item) => item.kind === 'file')
      .map((item) => item.getAsFileSystemHandle())
    const dirHandles: FileSystemDirectoryHandle[] = []

    for await (const handle of fileHandlesPromises) {
      if (handle instanceof FileSystemDirectoryHandle) {
        dirHandles.push(handle)
      }
    }

    handleFileSystemDirectories(dirHandles)
  }

  const handleSelectDirectory = () => {
    window
      .showDirectoryPicker()
      .then((handle) => {
        handleFileSystemDirectories([handle])
      })
      .catch(() => {})
  }

  return (
    <>
      <div className="flex gap-2 w-fit absolute left-4 right-4 bottom-6 z-1 mx-auto sm:mr-0 transition-[opacity,transform] duration-400">
        <QRCodeButton />
      </div>
      <Container>
        <div
          className={cn(
            'p-4 flex flex-col gap-4',
            syncingImages[albumId] && Object.keys(syncingImages[albumId]).length ? 'h-72' : 'h-body',
          )}
        >
          <div>
            <div className="flex items-center gap-2 ml-auto w-fit">
              <Checkbox
                id="sync-existed-images"
                checked={syncExistedImages}
                onCheckedChange={(checked) => setSyncExistedImages(checked === 'indeterminate' ? false : checked)}
              />
              <Label htmlFor="sync-existed-images">Đồng bộ các ảnh có sẵn trong thư mục</Label>
            </div>
          </div>
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={cn(
              'flex-1 flex flex-col justify-center items-center rounded-md transition-all select-none pb-4',
              isDragOver ? 'bg-secondary/50' : 'bg-transparent',
            )}
          >
            <Icon icon="material-symbols:create-new-folder-rounded" className="size-32 text-accent" />
            <p className="font-medium text-neutral-500 dark:text-neutral-400 pointer-events-none text-center">
              {isDragOver ? 'Thả thư mục để bắt đầu đồng bộ.' : 'Kéo thả thư mục bạn muốn đồng bộ vào đây.'}
            </p>
            <Button className="!rounded-full !px-6 mt-2 cursor-pointer" onClick={handleSelectDirectory}>
              <Icon icon="material-symbols:create-new-folder-rounded" />
              Chọn thư mục
            </Button>
          </div>
        </div>
        <div
          className="pb-4 px-4 space-y-8"
          hidden={!syncingImages[albumId] || !Object.keys(syncingImages[albumId]).length}
        >
          {Object.entries(syncingImages[albumId] ?? {}).map(([dirId, dir]) => (
            <div key={dirId} className="border-b pb-10">
              <div className="flex items-center gap-3 mb-2">
                <FolderSync />
                <span className="text-lg font-semibold inline-block">/{dir.handle.name}</span>
              </div>
              {!syncingImages[albumId]?.[dirId]?.images.length && <EmptyImageList />}
              <UploadingImageGrid images={syncingImages[albumId]?.[dirId]?.images ?? []} />
            </div>
          ))}
        </div>
      </Container>
    </>
  )
}

export default AutoSyncPage
