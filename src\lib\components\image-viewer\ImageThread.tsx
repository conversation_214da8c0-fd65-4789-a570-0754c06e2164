import { FC } from 'react'
import { cn } from '@/lib/utils'
import { Thread } from '@/lib/types'
import Editor from './Editor'
import { formatDistanceToNow } from 'date-fns'
import { vi } from 'date-fns/locale/vi'
import ImageThreadDetail from './ImageThreadDetail'
import UserAvatar from './UserAvatar'
import { AnimatePresence, motion } from 'motion/react'

interface ImageThreadProps {
  thread: Thread
  isViewDetail?: boolean
  className?: string
  onOpenChange?: (open: boolean) => void
}

const ImageThread: FC<ImageThreadProps> = ({ thread, isViewDetail, className, onOpenChange }) => {
  return (
    <>
      <div
        className="absolute size-0 pointer-events-auto select-text cursor-auto"
        style={{ left: `${thread.left}%`, top: `${thread.top}%`, zIndex: isViewDetail ? 1 : 0 }}
        onMouseDown={(e) => {
          e.stopPropagation()
        }}
      >
        <div
          className={cn(
            'comment-quick-view flex absolute left-0 bottom-0 bg-background shadow-lg transition-all duration-200 rounded-[18px] w-fit rounded-bl-xs group cursor-pointer',
            {
              'pointer-events-none': isViewDetail,
            },
            className,
          )}
          onMouseDown={() => {
            onOpenChange?.(true)
          }}
        >
          <div className="size-9 p-1.5 group-hover:translate-1 transition-transform duration-200 ease-out">
            <UserAvatar username={thread.username ?? 'Khách'} />
          </div>
          <div className="w-0 pt-5 grid grid-rows-[0fr] group-hover:grid-rows-[1fr] group-hover:w-64 group-hover:pt-0 overflow-hidden opacity-0 group-hover:opacity-100 transition-all duration-200 ease-out">
            <div className="w-64 px-3 py-2 -ml-1 text-sm min-h-0">
              <div className="flex mb-1 gap-1.5 items-baseline">
                <span className="font-bold">{thread.username ?? 'Khách'}</span>
                <span className="text-xs opacity-60">
                  {formatDistanceToNow(thread.created_at, { locale: vi })} trước
                </span>
              </div>
              <Editor value={thread.comments[0].content} editable={false} />
            </div>
          </div>
        </div>
        <AnimatePresence>
          {isViewDetail && (
            <motion.div
              initial={{ opacity: 0, scale: 0.2, translateX: -36 }}
              animate={{ opacity: 1, scale: 1, translateX: 0 }}
              exit={{ opacity: 0, scale: 0.2, translateX: -36 }}
              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
              className="absolute left-13.5 -top-13.5 origin-[0_36px]"
            >
              <ImageThreadDetail thread={thread} onClose={() => onOpenChange?.(false)} />
            </motion.div>
          )}
        </AnimatePresence>
        {/* {isViewDetail && <ImageThreadDetail thread={thread} onClose={() => onOpenChange?.(false)} />} */}
      </div>
    </>
  )
}

export default ImageThread
