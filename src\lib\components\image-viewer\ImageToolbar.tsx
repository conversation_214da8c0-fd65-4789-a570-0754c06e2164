import { AnimatePresence, motion } from 'motion/react'
import { FC, useEffect, useState } from 'react'
import { Too<PERSON><PERSON>, TooltipContent, TooltipTrigger } from '@/lib/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { Separator } from '@/lib/components/ui/separator'
import { ColorPicker } from '@/lib/components/ui/color-picker'
import { Input } from '@/lib/components/ui/input'
import { Slider } from '@/lib/components/ui/slider'
import { Toggle } from '@/lib/components/ui/toggle'
import { Eraser, Hand, Highlighter, MessageCircleMore, Pen, Search } from 'lucide-react'
import { Button } from '@/lib/components/ui/button'
import { drawToolColors } from '@/lib/constants'
import { useAtom, useAtomValue } from 'jotai'
import { brushColorAtom, brushSizeAtom, drawTypeAtom, selectionToolAtom } from '@/lib/atoms'

interface ImageToolbarProps {
  resetZoomAndPan?: () => void
}

const BrushColorPicker: FC = () => {
  const [brushColor, setBrushColor] = useAtom(brushColorAtom)
  const [brushColorLocal, setBrushColorLocal] = useState(brushColor)

  return (
    <ColorPicker
      value={brushColorLocal}
      onChange={(color) => {
        setBrushColorLocal(color)
      }}
      onValueCommit={(color) => {
        setBrushColor(color)
      }}
    >
      <div className="size-6 rounded-full color-swatch cursor-pointer"></div>
    </ColorPicker>
  )
}

const BrushSizeSlider: FC = () => {
  const drawType = useAtomValue(drawTypeAtom)
  const [brushSize, setBrushSize] = useAtom(brushSizeAtom)
  const [brushSizeLocal, setBrushSizeLocal] = useState(brushSize)

  useEffect(() => {
    setBrushSizeLocal(brushSize)
  }, [brushSize])

  return (
    <div className="flex flex-1 gap-2">
      <Input
        disabled={drawType === 'erase'}
        value={brushSizeLocal}
        onChange={(x) => {
          const value = Number(x.target.value.split(/[^0-9]+/g).join('') || '1')
          setBrushSize(value)
        }}
        className="py-0 w-24 h-7 px-2 text-xs"
      />
      <Slider
        disabled={drawType === 'erase'}
        min={1}
        max={200}
        step={1}
        value={[brushSizeLocal]}
        onValueChange={([value]) => {
          setBrushSizeLocal(value)
        }}
        onValueCommit={([value]) => {
          setBrushSize(value)
        }}
      />
    </div>
  )
}

const ImageToolbar: FC<ImageToolbarProps> = ({ resetZoomAndPan }) => {
  const [selectionTool, setSelectionTool] = useAtom(selectionToolAtom)
  const [drawType, setDrawType] = useAtom(drawTypeAtom)
  const [brushColor, setBrushColor] = useAtom(brushColorAtom)

  return (
    <div className="fixed left-0 right-0 bottom-0 flex flex-col justify-end items-center gap-2 pb-4">
      <AnimatePresence>
        {selectionTool === 'draw' && (
          <motion.div
            initial={{ opacity: 0, translateY: 75, scale: 0.5 }}
            animate={{ opacity: 1, translateY: 0, scale: 1 }}
            transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
            exit={{ opacity: 0, translateY: 75, scale: 0.5 }}
            className="flex flex-col rounded-xl bg-background pointer-events-auto"
          >
            <div
              className={cn('flex gap-2 justify-center items-center h-10 px-2 transition-all', {
                'opacity-60 pointer-events-none': drawType === 'erase',
              })}
            >
              {drawToolColors.map((color) => (
                <Tooltip key={color.hex}>
                  <TooltipTrigger asChild>
                    <div
                      className={cn('rounded-full size-6 border border-black/20 cursor-pointer', {
                        'outline-2 outline-offset-2 outline-primary':
                          color.hex === brushColor && selectionTool === 'draw' && drawType !== 'erase',
                      })}
                      style={{ backgroundColor: color.hex }}
                      onClick={() => {
                        setBrushColor(color.hex)
                      }}
                    ></div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <span>{color.label}</span>
                  </TooltipContent>
                </Tooltip>
              ))}
              <Separator orientation="vertical" />
              <BrushColorPicker />
            </div>
            <Separator orientation="horizontal" />
            <div className="flex gap-2 items-center px-2 h-12">
              <BrushSizeSlider />
              <Separator orientation="vertical" />
              <Toggle
                className="min-w-6 size-8 p-0 shrink-0"
                pressed={selectionTool === 'draw' && drawType === 'erase'}
                onClick={() => setDrawType('erase')}
              >
                <Eraser strokeWidth={1.5} absoluteStrokeWidth className="-translate-x-[1px]" />
              </Toggle>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <div
        className="px-2 relative w-fit h-14 mx-auto bg-background rounded-xl pointer-events-auto flex gap-2 justify-center items-center"
        onClick={(e) => {
          e.stopPropagation()
        }}
      >
        <Button variant="secondary" size="icon" className="size-10" onClick={resetZoomAndPan}>
          <Search className="size-4.5" strokeWidth={1.5} absoluteStrokeWidth />
        </Button>
        <Separator orientation="vertical" />
        <Toggle className="size-10" pressed={selectionTool === 'move'} onClick={() => setSelectionTool('move')}>
          <Hand className="size-4.5" strokeWidth={1.5} absoluteStrokeWidth />
        </Toggle>
        <div
          className={cn('flex justify-center items-center rounded-md transition-colors duration-400', {
            'bg-secondary': selectionTool === 'draw',
          })}
        >
          <Toggle pressed={selectionTool === 'draw'} className="size-10" onClick={() => setSelectionTool('draw')}>
            <Pen className="size-4.5" strokeWidth={1.5} absoluteStrokeWidth />{' '}
          </Toggle>
          <AnimatePresence>
            {selectionTool === 'draw' && (
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: 68 }}
                transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
                exit={{ width: 0 }}
                className="flex justify-end h-10 overflow-hidden"
              >
                <div className="w-17 flex justify-center items-center">
                  <Toggle
                    className="min-w-6 size-7 p-0 shrink-0"
                    pressed={drawType === 'marker'}
                    onClick={() => setDrawType('marker')}
                  >
                    <Pen strokeWidth={1.5} absoluteStrokeWidth />
                  </Toggle>
                  <Toggle
                    className="min-w-6 size-7 p-0 shrink-0"
                    pressed={drawType === 'highlight'}
                    onClick={() => setDrawType('highlight')}
                  >
                    <Highlighter strokeWidth={1.5} absoluteStrokeWidth />
                  </Toggle>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <Toggle pressed={selectionTool === 'comment'} className="size-10" onClick={() => setSelectionTool('comment')}>
          <MessageCircleMore className="size-4.5" strokeWidth={1.5} absoluteStrokeWidth />
        </Toggle>
      </div>
    </div>
  )
}

export default ImageToolbar
