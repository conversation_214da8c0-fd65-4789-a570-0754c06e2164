import { ImageIcon } from 'lucide-react'
import { FC } from 'react'
import { cn } from '../utils'

const EmptyImageList: FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('size-full flex flex-col items-center justify-center', className)}>
      <ImageIcon className="w-10 h-10 text-accent" />
      <p className="text-2xl font-semibold text-accent">Chưa có ảnh nào</p>
    </div>
  )
}

export default EmptyImageList
