'use client'

import { lastUpdatedAtom, userAtom } from '@/lib/atoms'
import AlbumCard from './AlbumCard'
import { axiosInstance } from '@/lib/axios'
import { Album } from '@/lib/types'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import Container from '@/lib/components/Container'
import { useState } from 'react'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/lib/components/ui/alert-dialog'
import { Button } from '@/lib/components/ui/button'
import { LoaderCircle } from 'lucide-react'
import { toast } from 'sonner'
import { AxiosError } from 'axios'
import AlbumInfo from './AlbumInfo'

const AlbumsPage: React.FC = () => {
  const [deleteAlbum, setDeleteAlbum] = useState<Album | null>(null)
  const [isDeleteAlbumOpen, setIsDeleteAlbumOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const lastUpdated = useAtomValue(lastUpdatedAtom)
  const user = useAtomValue(userAtom)
  const { data, refetch } = useQuery({
    queryKey: ['albums', lastUpdated['albums'], user?.username],
    placeholderData: keepPreviousData,
    queryFn: async ({ signal }) => {
      const res = await axiosInstance.get('/ptg/customer/list', { signal })

      return res.data
        ? {
            data: res.data.customer_list as Album[],
            page: res.data.stats.current_page as number,
            totalPage: res.data.stats.total_page as number,
          }
        : {
            data: [],
            page: 1,
            totalPage: 1,
          }
    },
  })

  function handleDeleteAlbum(albumId: string) {
    setIsDeleting(true)
    axiosInstance
      .delete(`/ptg/album/delete/${albumId}`)
      .then(() => {
        refetch({ cancelRefetch: true })
        toast.success('Xoá album thành công')
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin thiết lập không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setIsDeleting(false)
        setIsDeleteAlbumOpen(false)
      })
  }

  return (
    <>
      <Container className="py-4 px-responsive space-y-responsive md:space-y-4">
        {data?.data.map((album) => (
          <AlbumCard
            key={album.code}
            album={album}
            onDelete={() => {
              setDeleteAlbum(album)
              setIsDeleteAlbumOpen(true)
            }}
          />
        ))}
      </Container>
      <AlertDialog
        open={isDeleteAlbumOpen}
        onOpenChange={(open) => {
          if (!isDeleting) {
            setIsDeleteAlbumOpen(open)
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận</AlertDialogTitle>
            <AlertDialogDescription>Bạn có chắc chắn muốn xoá album này?</AlertDialogDescription>
          </AlertDialogHeader>
          {deleteAlbum && <AlbumInfo album={deleteAlbum} />}
          <AlertDialogFooter>
            <AlertDialogCancel>Huỷ</AlertDialogCancel>
            <Button onClick={() => handleDeleteAlbum(deleteAlbum?.code || '')} disabled={isDeleting}>
              {isDeleting && <LoaderCircle className="animate-spin" />}
              Đồng ý
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default AlbumsPage
