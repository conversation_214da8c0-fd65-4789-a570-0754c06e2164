'use client'

import { axiosInstance } from '@/lib/axios'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import { FC, useEffect, useState } from 'react'
import { useSetAtom } from 'jotai'
import { lastUpdatedAtom } from '@/lib/atoms'
import { toast } from 'sonner'
import { AxiosError } from 'axios'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/lib/components/ui/select'
import globeIcon from '@iconify-icons/mdi/globe'
import globeOffIcon from '@iconify-icons/mdi/globe-off'
import { Icon } from '@iconify/react'
import { ChevronRight, LoaderCircle } from 'lucide-react'
import { Button } from '@/lib/components/ui/button'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/lib/components/ui/alert-dialog'

interface SettingDialogProps {
  albumId: string
  isAlbumPublic: boolean
  isNamePublic: boolean
  passcode: string
  albumLastUpdate: string
  children?: React.ReactNode
}

const SettingDialog: FC<SettingDialogProps> = ({
  albumId,
  isAlbumPublic,
  children,
  passcode,
  isNamePublic,
  albumLastUpdate,
}) => {
  const setLastUpdated = useSetAtom(lastUpdatedAtom)
  const [isLoading, setIsLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [resetImagesPublicOpen, setResetImagesPublicOpen] = useState(false)
  const [resetImagesSelectionOpen, setResetImagesSelectionOpen] = useState(false)
  const [isAlbumPublicLocal, setIsAlbumPublicLocal] = useState(isAlbumPublic)
  const [isNamePublicLocal, setIsNamePublicLocal] = useState(isNamePublic)

  function setPublic(isAlbumPublic: boolean, isNamePublic: boolean) {
    setIsAlbumPublicLocal(isAlbumPublic)
    setIsNamePublicLocal(isNamePublic)
    axiosInstance
      .put(`/customer/album/setting/${albumId}`, {
        passcode,
        setpublic: isAlbumPublic,
        setnamepublic: isNamePublic,
      })
      .then(() => {
        setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin thiết lập không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
  }

  function resetImagesPublic() {
    setIsLoading(true)
    axiosInstance
      .delete(`/customer/album/set_image_visibility/${albumId}`, {
        data: {
          passcode,
        },
      })
      .then(() => {
        setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin thiết lập không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setIsLoading(false)
        setResetImagesPublicOpen(false)
      })
  }

  function resetImagesSelection() {
    setIsLoading(true)
    axiosInstance
      .delete(`/customer/album/pick/${albumId}`, {
        data: {
          passcode,
        },
      })
      .then(() => {
        setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin thiết lập không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setIsLoading(false)
        setResetImagesSelectionOpen(false)
      })
  }

  useEffect(() => {
    setIsAlbumPublicLocal(isAlbumPublic)
    setIsNamePublicLocal(isNamePublic)
  }, [albumLastUpdate, isAlbumPublic, isNamePublic])

  return (
    <>
      <Dialog
        open={open && !resetImagesPublicOpen && !resetImagesSelectionOpen}
        onOpenChange={(open) => {
          setOpen(open)
        }}
      >
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="focus-visible:outline-0">
          <VisuallyHidden>
            <DialogHeader>
              <DialogTitle></DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
          </VisuallyHidden>
          <div>
            <h2 className="text-lg leading-none font-semibold">Thiết lập chung</h2>
            <div className="space-y-0.5 mt-3 *:px-4 *:h-18 *:bg-accent *:first:rounded-t-sm *:last:rounded-b-sm">
              <div className="flex justify-between items-center gap-2">
                <span>Hiện thị tên cho</span>
                <Select
                  value={isNamePublicLocal ? 'public' : 'private'}
                  onValueChange={(value) => setPublic(isAlbumPublicLocal, value === 'public')}
                >
                  <SelectTrigger className="w-40 !bg-primary !text-primary-foreground font-medium">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">
                      <Icon icon={globeIcon} className="text-inherit" />
                      Công khai
                    </SelectItem>
                    <SelectItem value="private">
                      <Icon icon={globeOffIcon} className="text-inherit" />
                      Chỉ mình tôi
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-between items-center gap-2">
                <span>Ai có thể thấy được album này </span>
                <Select
                  value={isAlbumPublicLocal ? 'public' : 'private'}
                  onValueChange={(value) => setPublic(value === 'public', isNamePublicLocal)}
                >
                  <SelectTrigger className="w-40 !bg-primary !text-primary-foreground font-medium">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">
                      <Icon icon={globeIcon} className="text-inherit" />
                      Công khai
                    </SelectItem>
                    <SelectItem value="private">
                      <Icon icon={globeOffIcon} className="text-inherit" />
                      Chỉ mình tôi
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <div className="mt-2">
            <h2 className="text-lg leading-none font-semibold">Đặt lại album</h2>
            <div className="space-y-0.5 mt-3">
              <Button
                variant="accent"
                className="w-full h-18 justify-between text-base px-4 rounded-sm bg-accent text-foreground"
                onClick={() => setResetImagesPublicOpen(true)}
              >
                <span className="px-1">Reset ảnh về hết công khai</span>
                <ChevronRight className="size-6" />
              </Button>
              <Button
                variant="accent"
                className="w-full h-18 justify-between text-base px-4 rounded-sm bg-accent text-foreground"
                onClick={() => setResetImagesSelectionOpen(true)}
              >
                <span className="px-1">Reset hết ảnh đã thả tym</span>
                <ChevronRight className="size-6" />
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <AlertDialog
        open={resetImagesPublicOpen}
        onOpenChange={(open) => {
          if (!isLoading) {
            setResetImagesPublicOpen(open)
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận</AlertDialogTitle>
            <AlertDialogDescription>Bạn có chắc chắn muốn reset ảnh về hết công khai?</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Huỷ</AlertDialogCancel>
            <Button onClick={resetImagesPublic}>
              {isLoading && <LoaderCircle className="animate-spin" />}
              Đồng ý
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog
        open={resetImagesSelectionOpen}
        onOpenChange={(open) => {
          if (!isLoading) {
            setResetImagesSelectionOpen(open)
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận</AlertDialogTitle>
            <AlertDialogDescription>Bạn có chắc chắn muốn reset ảnh đã chọn?</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Huỷ</AlertDialogCancel>
            <Button onClick={resetImagesSelection}>
              {isLoading && <LoaderCircle className="animate-spin" />}
              Đồng ý
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default SettingDialog
