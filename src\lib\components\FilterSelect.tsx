import { Button } from '@/lib/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/lib/components/ui/select'
import radioboxMarkedIcon from '@iconify-icons/mdi/radiobox-marked'
import { Icon } from '@iconify/react'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'

export interface FilterSelectOption<T extends string> {
  value: T
  label: string
  hidden?: boolean
  disabled?: boolean
}

interface FilterSelectProps<T extends string> {
  value?: T
  defaultValue?: T
  onChange?: (value: T | undefined) => void
  options: FilterSelectOption<T>[]
  placeholder?: string
}
const indicator = <Icon icon={radioboxMarkedIcon} className="text-primary" />

const FilterSelect = <T extends string>({
  value,
  defaultValue,
  options,
  placeholder,
  onChange,
}: FilterSelectProps<T>) => {
  return (
    <div className="flex gap-0.5">
      <Select value={value ?? ''} onValueChange={(value) => onChange?.(value as T)}>
        <SelectTrigger
          className={cn(
            'border-primary gap-3',
            value !== defaultValue ? 'rounded-r-none !bg-primary text-background' : '!text-primary',
          )}
        >
          <SelectValue placeholder={placeholder && <span className="italic text-primary/40">{placeholder}</span>} />
        </SelectTrigger>
        <SelectContent className="border-primary bg-secondary">
          {options
            .filter((x) => !x.hidden)
            .map((option) => (
              <SelectItem disabled={option.disabled} key={option.value} value={option.value} indicator={indicator}>
                {option.label}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>
      {value !== defaultValue && (
        <Button size="icon" className="rounded-l-none" onClick={() => onChange?.(defaultValue)}>
          <X />
        </Button>
      )}
    </div>
  )
}

export default FilterSelect
