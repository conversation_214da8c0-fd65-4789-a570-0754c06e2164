'use client'

import { But<PERSON> } from '@/lib/components/ui/button'
import { useSetAtom } from 'jotai'
import { FC, useState } from 'react'
import { lastUpdatedAtom } from '@/lib/atoms'
import { ImagePlus } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { createAlbumSchema, CreateAlbumSchemaType } from '@/lib/zod-schema/album-schema'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/lib/components/ui/form'
import { Input } from '@/lib/components/ui/input'
import { addDays } from 'date-fns'
import { axiosInstance } from '@/lib/axios'
import { toast } from 'sonner'
import { AxiosError } from 'axios'
import { DateTimePicker } from '@/lib/components/ui/datetime-picker'
import { Album } from '@/lib/types'

interface AlbumInfoDialogProps {
  children: React.ReactNode
  editAlbum?: Album
  onSuccess?: () => void
}

const AlbumInfoDialog: FC<AlbumInfoDialogProps> = ({ children, onSuccess, editAlbum }) => {
  const setLastUpdated = useSetAtom(lastUpdatedAtom)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const form = useForm<CreateAlbumSchemaType>({
    defaultValues: {
      name: editAlbum?.customer_name || '',
      limit: editAlbum?.limit_image || 4,
      endDate: editAlbum?.end_date ? new Date(editAlbum?.end_date) : addDays(new Date(), 3),
    },
    resolver: zodResolver(createAlbumSchema),
  })

  function onSubmit(values: CreateAlbumSchemaType) {
    if (editAlbum) {
      toast.promise(
        axiosInstance
          .put(`/ptg/album/edit_info/${editAlbum.code}`, {
            customer_name: values.name,
            limit_image: values.limit,
            end_date: values.endDate.getTime(),
          })
          .then((res) => {
            setLastUpdated((prev) => ({ ...prev, albums: Date.now() }))
            setOpen(false)
            onSuccess?.()
            return res.data.message || 'Sửa thông tin album thành công'
          })
          .catch((err) => {
            if (err instanceof AxiosError) {
              if (err?.code === 'ERR_NETWORK') {
                throw 'Lỗi mạng, vui lòng thử lại sau'
              } else if (err.code === 'ERR_BAD_REQUEST') {
                throw err.response?.data.error.message || 'Thông tin sửa album không hợp lệ'
              }
            } else {
              console.error(err)
              throw 'Lỗi không xác định'
            }
          })
          .finally(() => {
            setLoading(false)
          }),
        {
          loading: 'Đang lưu thay đổi...',
          success: (msg) => msg ?? '',
          error: (msg) => msg,
        },
      )
      return
    }

    const params = new URLSearchParams()
    params.set('name', values.name)
    params.set('limit', values.limit.toString())
    params.set('end_date', values.endDate.getTime().toString())

    toast.promise(
      axiosInstance
        .post('/customer/album/create', null, { params })
        .then((res) => {
          setLastUpdated((prev) => ({ ...prev, albums: Date.now() }))
          setOpen(false)
          onSuccess?.()
          form.reset()
          return res.data.message || 'Tạo album thành công'
        })
        .catch((err) => {
          if (err instanceof AxiosError) {
            if (err?.code === 'ERR_NETWORK') {
              throw 'Lỗi mạng, vui lòng thử lại sau'
            } else if (err.code === 'ERR_BAD_REQUEST') {
              throw err.response?.data.error.message || 'Thông tin tạo album không hợp lệ'
            }
          } else {
            console.error(err)
            throw 'Lỗi không xác định'
          }
        })
        .finally(() => {
          setLoading(false)
        }),
      {
        loading: 'Đang tạo album...',
        success: (msg) => msg ?? '',
        error: (msg) => msg,
      },
    )
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!loading) {
          setOpen(open)
        }

        if (!open) {
          form.reset()
        }
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-secondary">
        <DialogHeader>
          <DialogTitle>{editAlbum ? 'Sửa thông tin album' : 'Tạo album mới'}</DialogTitle>
          <DialogDescription className="hidden"></DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form className="w-full p-4 space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              disabled={loading}
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên khách hàng</FormLabel>
                  <FormControl>
                    <Input {...field} type="text" className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              disabled={loading}
              control={form.control}
              name="limit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số lượng ảnh tối đa mà khách được thả tym</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" min={1} className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              disabled={loading}
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Deadline khách được chọn ảnh</FormLabel>
                  <FormControl>
                    <DateTimePicker
                      value={field.value}
                      onChange={field.onChange}
                      classNames={{ trigger: '!bg-accent shadow-none' }}
                      modal
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button type="submit" size="lg" className="sm:translate-x-4 translate-y-4 rounded-full w-full sm:w-auto">
                <ImagePlus />
                {editAlbum ? 'Lưu thay đổi' : 'Tạo mới album'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default AlbumInfoDialog
