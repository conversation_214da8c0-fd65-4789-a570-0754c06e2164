'use no memo'

import { FC, useEffect, useState } from 'react'
import { ColorPicker } from '@/lib/components/ui/color-picker'
import { tagSchema, TagSchemaType } from '@/lib/zod-schema/tag-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/lib/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/lib/components/ui/form'
import { Input } from '@/lib/components/ui/input'
import { Button } from '@/lib/components/ui/button'
import { useAtom, useSetAtom } from 'jotai'
import { editTagAtom, isTagDialogOpenAtom, lastUpdatedAtom } from '@/lib/atoms'
import tagPlusIcon from '@iconify-icons/mdi/tag-plus'
import { Icon } from '@iconify/react'
import Tag from '@/lib/components/Tag'
import { toast } from 'sonner'
import { axiosInstance } from '@/lib/axios'
import { AxiosError } from 'axios'

const TagDialog: FC = () => {
  const setLastUpdated = useSetAtom(lastUpdatedAtom)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useAtom(isTagDialogOpenAtom)
  const [editTag, setEditTag] = useAtom(editTagAtom)
  const form = useForm<TagSchemaType>({
    defaultValues: {
      name: editTag?.name || '',
      color: editTag?.color || '#f4741c',
    },
    resolver: zodResolver(tagSchema),
  })
  const name = form.watch('name')
  const color = form.watch('color')

  function onSubmit(values: TagSchemaType) {
    if (editTag) {
      toast.promise(
        axiosInstance
          .put('/ptg/tags/modify', {
            id: editTag.id,
            new_name: values.name,
            new_color: values.color.slice(1),
          })
          .then((res) => {
            setLastUpdated((prev) => ({ ...prev, ['tags']: Date.now() }))
            setEditTag(null)
            setOpen(false)
            setTimeout(form.reset, 200)
            return res.data.message || 'Sửa thông tin tag thành công'
          })
          .catch((err) => {
            if (err instanceof AxiosError) {
              if (err?.code === 'ERR_NETWORK') {
                throw 'Lỗi mạng, vui lòng thử lại sau'
              } else if (err.code === 'ERR_BAD_REQUEST') {
                throw err.response?.data.error.message || 'Thông tin sửa tag không hợp lệ'
              }
            } else {
              console.error(err)
              throw 'Lỗi không xác định'
            }
          })
          .finally(() => {
            setLoading(false)
          }),
        {
          loading: 'Đang lưu thay đổi...',
          success: (msg) => msg ?? '',
          error: (msg) => msg,
        },
      )
      return
    }

    toast.promise(
      axiosInstance
        .post('/ptg/tags/create', {
          name: values.name,
          color: values.color.slice(1),
        })
        .then((res) => {
          setLastUpdated((prev) => ({ ...prev, ['tags']: Date.now() }))
          setOpen(false)
          setTimeout(form.reset, 200)
          return res.data.message || 'Tạo tag thành công'
        })
        .catch((err) => {
          if (err instanceof AxiosError) {
            if (err?.code === 'ERR_NETWORK') {
              throw 'Lỗi mạng, vui lòng thử lại sau'
            } else if (err.code === 'ERR_BAD_REQUEST') {
              throw err.response?.data.error.message || 'Thông tin tạo tag không hợp lệ'
            }
          } else {
            console.error(err)
            throw 'Lỗi không xác định'
          }
        })
        .finally(() => {
          setLoading(false)
        }),
      {
        loading: 'Đang tạo tag...',
        success: (msg) => msg ?? '',
        error: (msg) => msg,
      },
    )
  }

  useEffect(() => {
    if (open && editTag) {
      form.setValue('name', editTag.name)
      form.setValue('color', `#${editTag.color}`)
    }
  }, [open, editTag, form])

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!loading) {
          setOpen(open)
        }

        if (!open) {
          setEditTag(null)
          setTimeout(form.reset, 200)
        }
      }}
    >
      <DialogContent className="sm:max-w-[425px] bg-secondary">
        <DialogHeader>
          <DialogTitle>{editTag ? 'Sửa thông tin tag' : 'Tạo tag mới'}</DialogTitle>
          <DialogDescription className="hidden"></DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form className="w-full p-4 space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              disabled={loading}
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên</FormLabel>
                  <FormControl>
                    <Input {...field} type="text" className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              disabled={loading}
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Màu</FormLabel>
                  <FormControl>
                    <div className="flex gap-2 items-center">
                      <ColorPicker {...field} />
                      <span>{field.value}</span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Xem trước</p>
              <div
                style={{
                  backgroundImage: `url(/images/tag_test.jpg)`,
                }}
                className="relative h-20 rounded-md bg-cover bg-center"
              >
                <Tag
                  className="absolute left-2 bottom-2"
                  tag={{
                    id: 0,
                    name: name || 'Tên tag',
                    color: color.slice(1),
                    created_at: '',
                    last_updated_at: '',
                  }}
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button type="submit" size="lg" className="sm:translate-x-4 translate-y-4 rounded-full w-full sm:w-auto">
                <Icon icon={tagPlusIcon} />
                {editTag ? 'Lưu thay đổi' : 'Tạo mới tag'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default TagDialog
