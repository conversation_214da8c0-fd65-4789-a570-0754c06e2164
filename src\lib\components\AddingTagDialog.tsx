import { FC, useEffect, useMemo, useState } from 'react'
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/lib/components/ui/dialog'
import { Button } from '@/lib/components/ui/button'
import { LoaderCircle, Minus, MoveRight, Plus, RotateCcw, Save, X } from 'lucide-react'
import { addingTagImagesAtom, lastUpdatedAtom, userAtom } from '@/lib/atoms'
import { useAtom, useAtomValue } from 'jotai'
import { imageBaseUrl } from '@/lib/constants'
import { CustomerImage, ImageTag } from '@/lib/types'
import { ScrollArea } from '@/lib/components/ui/scroll-area'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { axiosInstance } from '@/lib/axios'
import { Input } from '@/lib/components/ui/input'
import { Checkbox } from '@/lib/components/ui/checkbox'
import Tag from '@/lib/components/Tag'
import { cn } from '@/lib/utils'
import { CheckedState } from '@radix-ui/react-checkbox'
import { toast } from 'sonner'
import tagIcon from '@iconify-icons/mdi/tag'
import { Icon } from '@iconify/react/dist/iconify.js'

interface TagState {
  tag: ImageTag
  checked: CheckedState
  images: CustomerImage[]
  willChange: boolean
  newState: boolean
}

const getState = (imgCount: number, total: number) => {
  return imgCount === 0 ? false : imgCount === total ? true : 'indeterminate'
}

const AddingTagDialog: FC = () => {
  const [addingTagImages, setAddingTagImages] = useAtom(addingTagImagesAtom)
  const user = useAtomValue(userAtom)
  const [lastUpdated, setLastUpdated] = useAtom(lastUpdatedAtom)
  const [open, setOpen] = useState(!!addingTagImages)
  const [selectedImages, setSelectedImages] = useState<CustomerImage[]>([])
  const [tagStates, setTagStates] = useState<TagState[]>([])
  const tagsWithChanges = useMemo(() => tagStates.filter((state) => state.willChange), [tagStates])
  const tagsToAdd = useMemo(() => tagsWithChanges.filter((state) => state.newState), [tagsWithChanges])
  const tagsToRemove = useMemo(() => tagsWithChanges.filter((state) => !state.newState), [tagsWithChanges])
  const [isLoading, setIsLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const { data: tags } = useQuery({
    queryKey: ['tags', user?.username, lastUpdated['tags']],
    placeholderData: keepPreviousData,
    queryFn: async ({ signal }) => {
      const res = await axiosInstance.get('/ptg/tags/list', { signal })

      if (res.status === 200) {
        return res.data.tags_list as ImageTag[]
      }

      return []
    },
  })
  const filteredTags = useMemo(() => {
    const lowerSearchText = searchText.toLowerCase()
    return tagStates.filter((x) => x.tag.name.toLowerCase().includes(lowerSearchText)) || []
  }, [tagStates, searchText])

  const handleCheck = (tagId: number, checked: CheckedState) => {
    setTagStates((prev) => {
      return prev.map((x) => {
        if (x.tag.id === tagId) {
          return {
            ...x,
            willChange: (checked ? selectedImages.length : 0) !== x.images.length,
            newState: checked,
          } as TagState
        }

        return x
      })
    })
  }

  const resetCheck = (tagId: number) => {
    setTagStates((prev) => {
      return prev.map((x) => {
        if (x.tag.id === tagId) {
          return {
            ...x,
            willChange: false,
          } as TagState
        }

        return x
      })
    })
  }

  const handleSave = () => {
    const promises: Promise<never>[] = []

    if (tagsToAdd.length > 0) {
      promises.push(
        axiosInstance.post(`/ptg/tags/image_tags_handler/${addingTagImages?.albumId}`, {
          image_id: addingTagImages?.images.map((x) => x.id),
          tag_id: tagsToAdd.map((x) => x.tag.id),
        }),
      )
    }

    if (tagsToRemove.length > 0) {
      promises.push(
        axiosInstance.delete(`/ptg/tags/image_tags_handler/${addingTagImages?.albumId}`, {
          data: {
            image_id: addingTagImages?.images.map((x) => x.id),
            tag_id: tagsToRemove.map((x) => x.tag.id),
          },
        }),
      )
    }

    setIsLoading(true)
    toast.promise(
      Promise.all(promises)
        .then(() => {
          setLastUpdated((prev) => ({ ...prev, [`album:${addingTagImages?.albumId}`]: Date.now() }))
          return 'Thay đổi thành công'
        })
        .finally(() => {
          setIsLoading(false)
          setAddingTagImages(null)
        }),
      {
        loading: 'Đang lưu thay đổi...',
        success: (msg) => msg,
        error: (msg) => msg,
      },
    )
  }

  useEffect(() => {
    if (addingTagImages) {
      setOpen(true)
      setSelectedImages(addingTagImages.images)

      const tagMap = addingTagImages.images.reduce(
        (acc, image) => {
          image.imageTags.forEach((tag) => {
            if (!acc[tag.id]) {
              acc[tag.id] = [image]
            } else {
              acc[tag.id].push(image)
            }
          })

          return acc
        },
        {} as Record<number, CustomerImage[]>,
      )

      setTagStates([
        ...Object.entries(tagMap)
          .reduce((acc, [id, images]) => {
            const tag = tags?.find((x) => x.id === Number(id))

            if (tag) {
              acc.push({
                tag,
                checked: getState(images.length, addingTagImages.images.length),
                images,
                willChange: false,
                newState: false,
              })
            }

            return acc
          }, [] as TagState[])
          .sort((a, b) => b.images.length - a.images.length),
        ...(tags
          ?.filter((x) => !tagMap[x.id])
          ?.map((x) => ({
            tag: x,
            checked: false,
            images: [],
            willChange: false,
            newState: false,
          })) || []),
      ])
    } else {
      setOpen(false)
      setTimeout(() => {
        setSelectedImages([])
        setTagStates([])
      }, 200)
    }
  }, [addingTagImages, tags])

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!isLoading && !open) {
          setAddingTagImages(null)
        }
      }}
    >
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Sửa tags cho {selectedImages.length} ảnh</DialogTitle>
          <DialogDescription>Bật/tắt checkbox để thêm hoặc xóa khỏi tất cả ảnh đã chọn</DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-48 mb-4 -mr-3">
          <div className="grid grid-cols-5 gap-4 pr-3">
            {selectedImages.map((x) => (
              <div key={x.id} className="aspect-square">
                <img
                  src={`${imageBaseUrl}${x?.imageOptimized.imageNameOptimized}`}
                  alt={x?.imageOptimized.imageNameOptimized}
                  className="size-full object-contain object-center rounded-xs"
                />
              </div>
            ))}
          </div>
        </ScrollArea>
        <Input value={searchText} onChange={(e) => setSearchText(e.target.value)} placeholder="Tìm kiếm tag" />
        <ScrollArea className="max-h-40 -mr-3 -mt-1">
          <div className="flex flex-col gap-2 pr-3">
            {filteredTags.length === 0 && (
              <div className="h-20 flex flex-col items-center justify-center">
                <Icon icon={tagIcon} className="w-10 h-10 text-accent" />
                <p className="text-2xl font-semibold text-accent">Chưa có tag nào</p>
              </div>
            )}
            {filteredTags.map(({ tag, images, checked, willChange, newState }) => (
              <div
                key={tag.id}
                className={cn(
                  'flex items-center gap-2 p-2 rounded-md transition-all',
                  willChange ? 'bg-accent' : 'bg-secondary',
                )}
              >
                <Tag tag={tag} />
                <span
                  className={cn(
                    'text-sm',
                    images.length === 0
                      ? 'text-gray-500'
                      : images.length === selectedImages.length
                        ? 'text-green-500'
                        : 'text-yellow-500',
                  )}
                >
                  {images.length}/{selectedImages.length}
                </span>
                {willChange && (
                  <>
                    <MoveRight />
                    <span className={cn('text-sm', newState ? 'text-green-500' : 'text-gray-500')}>
                      {newState ? selectedImages.length : 0}/{selectedImages.length}
                    </span>
                  </>
                )}
                <div className="ml-auto flex gap-2">
                  <Button
                    className="size-6 rounded"
                    variant="link"
                    hidden={!willChange}
                    onClick={() => resetCheck(tag.id)}
                  >
                    <RotateCcw />
                  </Button>
                  <Checkbox
                    className="size-6 border-primary"
                    checked={willChange ? newState : checked}
                    onCheckedChange={(checked) => handleCheck(tag.id, checked)}
                  />
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        {tagsWithChanges.length > 0 && (
          <div>
            <p className="font-medium">Thay đổi sẽ áp dụng:</p>
            {tagsToAdd.length > 0 && (
              <div className="flex gap-2 items-start mt-3">
                <div className="text-sm text-green-500 flex gap-2 items-center font-medium shrink-0 h-6">
                  <Plus strokeWidth={2.5} className="size-4" />
                  Thêm vào tất cả:
                </div>
                <div className="flex flex-wrap gap-1 flex-1">
                  {tagsToAdd.map((x) => (
                    <div key={x.tag.id} className="relative group">
                      <Tag tag={x.tag} />
                      <Button
                        variant="destructive"
                        className="absolute !p-0 h-4 w-6 -top-2 -right-2 rounded-full !bg-destructive opacity-0 group-hover:opacity-100"
                        onClick={() => resetCheck(x.tag.id)}
                      >
                        <X />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {tagsToRemove.length > 0 && (
              <div className="flex gap-2 items-start mt-3">
                <div className="text-sm text-red-500 flex gap-2 items-center font-medium shrink-0 h-6">
                  <Minus strokeWidth={2.5} className="size-4" />
                  Xóa khỏi tất cả:
                </div>
                <div className="flex flex-wrap gap-1 flex-1">
                  {tagsToRemove.map((x) => (
                    <div key={x.tag.id} className="relative group">
                      <Tag tag={x.tag} />
                      <Button
                        variant="destructive"
                        className="absolute !p-0 h-4 w-6 -top-2 -right-2 rounded-full !bg-destructive opacity-0 group-hover:opacity-100"
                        onClick={() => resetCheck(x.tag.id)}
                      >
                        <X />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Huỷ</Button>
          </DialogClose>
          <Button onClick={handleSave} disabled={isLoading || tagsWithChanges.length === 0}>
            {isLoading ? <LoaderCircle className="animate-spin" /> : <Save />}
            Lưu lại
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default AddingTagDialog
