'use client'

import { axiosInstance } from '@/lib/axios'
import { But<PERSON>, buttonVariants } from '@/lib/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/lib/components/ui/dialog'
import { setPasscodeSchema, SetPasscodeSchemaType } from '@/lib/zod-schema/set-passcode-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import warningRounedIcon from '@iconify-icons/material-symbols/warning-rounded'
import { Icon } from '@iconify/react'
import { cn } from '@/lib/utils'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/lib/components/ui/form'
import { Input } from '@/lib/components/ui/input'
import { LoaderCircle } from 'lucide-react'
import { useSet<PERSON>tom } from 'jotai'
import { lastUpdatedAtom } from '@/lib/atoms'
import { toast } from 'sonner'
import { AxiosError } from 'axios'

interface SetPasscodeDialogProps {
  albumId: string
  open: boolean
  setPasswordOnOpen?: boolean
  onClose?: () => void
  onReject?: () => void
}

const SetPasscodeDialog: FC<SetPasscodeDialogProps> = ({ albumId, open, setPasswordOnOpen, onClose, onReject }) => {
  const setLastUpdated = useSetAtom(lastUpdatedAtom)
  const [loading, setLoading] = useState(false)
  const [isSetPasscode, setIsSetPasscode] = useState(false)
  const form = useForm<SetPasscodeSchemaType>({
    defaultValues: {
      passcode: '',
      confirmPasscode: '',
    },
    resolver: zodResolver(setPasscodeSchema),
  })

  function onSubmit(values: SetPasscodeSchemaType) {
    setLoading(true)
    axiosInstance
      .post(`/customer/album/passcode_setting/${albumId}`, {
        passcode: values.passcode,
      })
      .then((res) => {
        setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
        onClose?.()
        toast.success(res.data.message || `Đặt mật khẩu cho album ${albumId} thành công`)
        form.reset()
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin đặt mật khẩu không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  useEffect(() => {
    if (setPasswordOnOpen && open) {
      setIsSetPasscode(true)
    }
  }, [open, setPasswordOnOpen])

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (loading) return
        if (!open) {
          onClose?.()
          setTimeout(() => {
            setIsSetPasscode(false)
            setLoading(false)
            form.reset()
          }, 200)
        }
      }}
    >
      <DialogContent
        className={cn('sm:max-w-[425px] bg-secondary focus-visible:outline-0', { '*:last:hidden': !isSetPasscode })}
      >
        <DialogHeader className={cn({ hidden: isSetPasscode })}>
          {!isSetPasscode && <Icon icon={warningRounedIcon} className="size-32 text-primary mx-auto" />}
          <DialogTitle
            className={cn('text-base font-medium text-foreground', {
              'text-center': !isSetPasscode,
            })}
          >
            {isSetPasscode ? 'Đặt mật khẩu' : 'Chú ý! album này chưa đặt mật khẩu.'}
          </DialogTitle>
          <DialogDescription className={cn('text-center text-foreground', { hidden: isSetPasscode })}>
            Nếu bạn là khách, vui lòng đặt mật khẩu cho album.
          </DialogDescription>
        </DialogHeader>
        {isSetPasscode && (
          <Form {...form}>
            <form className="w-full max-w-sm py-4 space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
              <FormField
                disabled={loading}
                control={form.control}
                name="passcode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                disabled={loading}
                control={form.control}
                name="confirmPasscode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Xác nhận mật khẩu</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end">
                <Button type="submit" size="lg" className="translate-y-4 rounded-full" disabled={loading}>
                  {loading && <LoaderCircle className="animate-spin" />}
                  Xác nhận
                </Button>
              </div>
            </form>
          </Form>
        )}
        {!isSetPasscode && (
          <DialogFooter className="grid grid-cols-2">
            <Button
              variant="default"
              onClick={() => {
                setIsSetPasscode(true)
                form.setFocus('passcode')
              }}
            >
              Oge, để đặt
            </Button>
            <DialogClose asChild>
              <Button onClick={onReject} className={buttonVariants({ variant: 'ghost', className: 'bg-accent' })}>
                {!isSetPasscode ? 'Tôi chỉ xem album' : 'Hủy'}
              </Button>
            </DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default SetPasscodeDialog
