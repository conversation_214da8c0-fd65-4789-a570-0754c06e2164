'use client'

import { FC, useState } from 'react'
import { Input } from '@/lib/components/ui/input'
import { Button } from '@/lib/components/ui/button'
import { ScanLine } from 'lucide-react'
import { useDebounce } from '@uidotdev/usehooks'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { axiosInstance } from '@/lib/axios'
import { Album } from '@/lib/types'
import SearchAlbumCard from '@/lib/components/SearchAlbumCard'
import { IDetectedBarcode, Scanner, useDevices } from '@yudiel/react-qr-scanner'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/lib/components/ui/select'
import { AnimatePresence, motion } from 'motion/react'
import { useRouter } from 'next/navigation'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/lib/components/ui/dialog'
import { ScrollArea } from '@/lib/components/ui/scroll-area'
import { useAtom } from 'jotai'
import { isSearchAlbumOpenAtom } from '@/lib/atoms'

interface AlbumSearchProps {
  children?: React.ReactNode
}

const AlbumSearch: FC<AlbumSearchProps> = ({ children }) => {
  const [keyword, setKeyword] = useState('')
  const debouncedKeyword = useDebounce(keyword, 300)
  const { data } = useQuery({
    queryKey: ['searchAlbum', debouncedKeyword],
    initialData: [],
    placeholderData: keepPreviousData,
    queryFn: async ({ signal }) => {
      if (debouncedKeyword.length <= 3) return []

      const searchParams = new URLSearchParams()
      searchParams.set('keyword', debouncedKeyword)

      const res = await axiosInstance.get(`/album/find?${searchParams}`, { signal })

      if (res.status === 204) {
        return []
      }

      return res.data.customer_list as Album[]
    },
  })
  const devices = useDevices()
  const [selectedDevice, setSelectedDevice] = useState<MediaDeviceInfo | null>(null)
  const router = useRouter()
  const [isSearchAlbumOpen, setIsSearchAlbumOpen] = useAtom(isSearchAlbumOpenAtom)

  const handleScan = (results: IDetectedBarcode[]) => {
    try {
      const origin = window.location.origin

      const match = results.map((x) => x.rawValue).find((x) => new URL(x).origin === origin)
      if (!match) return

      router.push(match)
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <>
      <Dialog
        open={isSearchAlbumOpen}
        onOpenChange={(open) => {
          setIsSearchAlbumOpen(open)
          if (!open) {
            setSelectedDevice(null)
          }
        }}
      >
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="sm:max-w-xl bg-secondary focus-visible:outline-0 px-2" aria-describedby={undefined}>
          <ScrollArea className="max-h-[calc(100vh-5.5rem)] min-h-[calc(60vh-5.5rem)] px-2">
            <div className="px-2">
              <DialogHeader className="mb-2">
                <DialogTitle>Tìm kiếm album</DialogTitle>
              </DialogHeader>
              <AnimatePresence>
                {!!selectedDevice && (
                  <motion.div
                    initial={{ opacity: 0, paddingTop: '0%', paddingBottom: 0 }}
                    animate={{ opacity: 1, paddingTop: '100%', paddingBottom: 8 }}
                    exit={{ opacity: 0, paddingTop: '0%', paddingBottom: 0 }}
                    transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
                    className="overflow-hidden h-0"
                  >
                    <div className="relative aspect-square -translate-y-full rounded-md overflow-hidden">
                      <div className="absolute inset-0 flex justify-center items-center">
                        <span className="font-semibold text-2xl">Đang tải camera</span>
                      </div>
                      <div className="relative">
                        <Scanner
                          constraints={{ deviceId: selectedDevice.deviceId }}
                          onScan={handleScan}
                          allowMultiple={false}
                          sound={false}
                        />
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
              <div className="flex gap-2">
                {selectedDevice ? (
                  <Select
                    value={selectedDevice.deviceId}
                    onValueChange={(value) => setSelectedDevice(devices.find((x) => x.deviceId === value) ?? null)}
                  >
                    <SelectTrigger className="w-full font-medium">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {devices
                        .filter((x) => x.deviceId)
                        .map((device) => (
                          <SelectItem key={device.deviceId} value={device.deviceId}>
                            {device.label}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <Input
                    placeholder="Nhập ID album hoặc tên khách"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                    type="text"
                  />
                )}
                <Button
                  size="icon"
                  variant={selectedDevice ? 'default' : 'outline'}
                  disabled={devices.length <= 0}
                  onClick={() => setSelectedDevice(selectedDevice ? null : devices[0])}
                >
                  <ScanLine />
                </Button>
              </div>
              <div className="flex flex-col gap-2 mt-2">
                {data.map((album) => (
                  <SearchAlbumCard key={album.code} album={album} />
                ))}
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AlbumSearch
