'use client'

import { FC, useState } from 'react'
import { Button } from '@/lib/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Input } from '@/lib/components/ui/input'
import { useForm } from 'react-hook-form'
import { Form, FormMessage, FormControl, FormLabel, FormItem, FormField } from '@/lib/components/ui/form'
import { RegisterFormSchema, RegisterFormSchemaType } from '@/lib/zod-schema/auth-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { axiosInstance } from '@/lib/axios'
import { toast } from 'sonner'
import Link from 'next/link'
import { AxiosError } from 'axios'

const RegisterPage: FC = () => {
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const form = useForm<RegisterFormSchemaType>({
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
    resolver: zodResolver(RegisterFormSchema),
  })

  function onSubmit(values: RegisterFormSchemaType) {
    setLoading(true)
    axiosInstance
      .post('/api/registration', values)
      .then((res) => {
        toast.success(res.data.message || 'Đăng ký thành công')
        router.replace('/login')
        form.reset()
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin đăng ký không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-56px)] h-0">
      <Form {...form}>
        <form className="w-full max-w-xl p-4" onSubmit={form.handleSubmit(onSubmit)} noValidate>
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl text-center">Đăng ký</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-4">
              <FormField
                disabled={loading}
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên đăng nhập</FormLabel>
                    <FormControl>
                      <Input {...field} type="text" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                disabled={loading}
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} type="email" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                disabled={loading}
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                disabled={loading}
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nhập lại mật khẩu</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={loading}>
                Đăng ký
              </Button>
            </CardFooter>
            <p className="text-xs text-center">
              Bạn đã có tài khoản?{' '}
              <Link href="/login" className="text-primary hover:underline">
                Đăng nhập
              </Link>
            </p>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default RegisterPage
