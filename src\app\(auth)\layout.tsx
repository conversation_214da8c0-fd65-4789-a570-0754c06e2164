'use client'

import { useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAtomValue } from 'jotai'
import { userAtom } from '@/lib/atoms'
import TagDialog from '@/lib/components/TagDialog'

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const user = useAtomValue(userAtom)
  const pathname = usePathname()

  useEffect(() => {
    if (!user) {
      let loginPath = '/login'

      if (pathname !== '/') {
        loginPath += `?redirect=${pathname}`
      }

      router.replace(loginPath)
    }
  }, [user, router, pathname])

  if (!user) {
    return null
  }

  return (
    <>
      {children}
      <TagDialog />
    </>
  )
}
