'use client'

import { useCopyToClipboard } from '@uidotdev/usehooks'
import { Copy } from 'lucide-react'
import { FC, useState } from 'react'
import { Button } from './ui/button'
import { toast } from 'sonner'

const CopyText: FC<{ text: string; children?: React.ReactNode }> = ({ text, children }) => {
  const [toastCoolDown, setToastCoolDown] = useState(false)
  const [, copyToClipboard] = useCopyToClipboard()

  const handleCopyAlbumCode = () => {
    copyToClipboard(text)

    if (!toastCoolDown) {
      setToastCoolDown(true)
      const truncatedText = text.slice(0, 80)
      toast.success(`Đã copy ${truncatedText}${truncatedText.length < text.length ? '...' : ''}`)

      const timeout = setTimeout(() => {
        setToastCoolDown(false)
      }, 5000)

      return () => clearTimeout(timeout)
    }
  }

  return (
    <div className="flex justify-end items-center gap-1">
      {children}
      <Button variant="ghost" size="icon" className="size-6" onClick={handleCopyAlbumCode}>
        <Copy />
      </Button>
    </div>
  )
}

export default CopyText
