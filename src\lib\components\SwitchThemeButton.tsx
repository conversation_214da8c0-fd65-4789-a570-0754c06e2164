'use client'

import React, { useEffect, useState } from 'react'
import { Button } from './ui/button'
import { useTheme } from 'next-themes'
import { Icon } from '@iconify/react'
import sunnyRoundedIcon from '@iconify-icons/material-symbols/sunny-rounded'

const SwitchThemeButton = () => {
  const { resolvedTheme, setTheme } = useTheme()
  const [isDark, setIsDark] = useState(false)

  useEffect(() => {
    setIsDark(resolvedTheme === 'dark')
  }, [resolvedTheme])

  return (
    <div className="flex" onClick={() => setTheme(isDark ? 'light' : 'dark')}>
      <Button
        variant={isDark ? 'secondary' : 'default'}
        className="rounded-r-none rounded-l-full !px-0 transition-all duration-400 w-9 dark:w-5"
      >
        <Icon icon={sunnyRoundedIcon} className="size-5 dark:size-3 transition-transform duration-400" />
      </Button>
      <Button
        variant={isDark ? 'default' : 'secondary'}
        className="rounded-l-none rounded-r-full !px-0 transition-all duration-400 w-5 dark:w-9"
      >
        <Icon icon="solar:moon-bold" className="size-3 dark:size-5 transition-transform duration-400" />
      </Button>
    </div>
  )
}

export default SwitchThemeButton
