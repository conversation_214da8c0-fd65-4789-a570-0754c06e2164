import { cn, stringToColor } from '@/lib/utils'
import { FC } from 'react'

interface UserAvatarProps extends React.ComponentProps<'div'> {
  username: string
  className?: string
}

const UserAvatar: FC<UserAvatarProps> = ({ username, className, ...props }) => {
  return (
    <div
      {...props}
      className={cn('size-6 rounded-full flex justify-center items-center', className)}
      style={{
        backgroundColor: stringToColor(username),
      }}
    >
      {username?.slice(0, 1)}
    </div>
  )
}

export default UserAvatar
