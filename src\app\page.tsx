'use client'

import { isSearchAlbumOpenAtom } from '@/lib/atoms'
import { Button } from '@/lib/components/ui/button'
import { cn } from '@/lib/utils'
import { useAtom } from 'jotai'
import { Search } from 'lucide-react'

const HomePage: React.FC = () => {
  const [isSearchAlbumOpen, setIsSearchAlbumOpen] = useAtom(isSearchAlbumOpenAtom)

  return (
    <>
      <div className="h-full flex justify-center items-center py-4 px-responsive">
        <Button
          variant="accent"
          onClick={() => setIsSearchAlbumOpen(true)}
          className={cn('max-w-xl w-full justify-start', { 'opacity-0 pointer-events-none': isSearchAlbumOpen })}
        >
          <Search />
          Tìm kiếm album...
        </Button>
      </div>
    </>
  )
}

export default HomePage
