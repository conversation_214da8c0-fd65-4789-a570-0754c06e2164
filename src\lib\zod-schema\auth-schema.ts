import { z } from 'zod'

export const LoginFormSchema = z.object({
  username: z.string().min(1, { message: 'Tên đăng nhập không được để trống.' }).trim(),
  password: z.string().min(1, { message: '<PERSON><PERSON><PERSON> khẩu không được để trống.' }).trim(),
})

export type LoginFormSchemaType = z.infer<typeof LoginFormSchema>

export const RegisterFormSchema = z
  .object({
    username: z.string().min(1, { message: 'Tên đăng nhập không được để trống.' }).trim(),
    email: z.string().email({ message: '<PERSON><PERSON> không hợp lệ.' }).trim(),
    password: z.string().min(1, { message: 'M<PERSON>t khẩu không được để trống.' }).trim(),
    confirmPassword: z.string().min(1, { message: '<PERSON><PERSON><PERSON> khẩu không được để trống.' }).trim(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'M<PERSON>t khẩu không khớp',
  })

export type RegisterFormSchemaType = z.infer<typeof RegisterFormSchema>
