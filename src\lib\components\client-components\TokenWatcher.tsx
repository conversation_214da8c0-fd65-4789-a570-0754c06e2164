'use client'

import { userAtom } from '@/lib/atoms'
import { useSetAtom } from 'jotai'
import { FC, useEffect } from 'react'

const TokenWatcher: FC = () => {
  const setUser = useSetAtom(userAtom)

  useEffect(() => {
    const onStorageChange = (e: StorageEvent) => {
      if (e.key === 'token' && (!e.newValue || (e.oldValue && e.newValue))) {
        setUser(e.newValue)
      }
    }
    window.addEventListener('storage', onStorageChange)

    return () => {
      window.removeEventListener('storage', onStorageChange)
    }
  }, [setUser])

  return <></>
}

export default TokenWatcher
