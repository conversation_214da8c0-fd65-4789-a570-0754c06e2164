'use client'

import { screenSize<PERSON>tom } from '@/lib/atoms'
import { screenSizeBreakpoints } from '@/lib/constants'
import { useSetAtom } from 'jotai'
import { FC, useEffect, useRef } from 'react'

const ScreenSizeWatcher: FC = () => {
  const screenBreakpoints = useRef(Object.values(screenSizeBreakpoints).sort((a, b) => b - a))
  const setScreenSize = useSetAtom(screenSizeAtom)

  useEffect(() => {
    const handleResize = () => {
      const screenSize = screenBreakpoints.current.find((breakpoint) => window.innerWidth > breakpoint)
      setScreenSize(screenSize ?? screenBreakpoints.current[0])
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }, [setScreenSize])

  return <></>
}

export default ScreenSizeWatcher
