'use client'

import { album<PERSON><PERSON><PERSON><PERSON><PERSON>, lastUpdated<PERSON>tom, rejectSetPasscodeAlbumIdsAtom, user<PERSON>tom } from '@/lib/atoms'
import { axiosInstance } from '@/lib/axios'
import AlbumMasonry from '@/lib/components/AlbumMasonry'
import Container from '@/lib/components/Container'
import { Album, CustomerImage } from '@/lib/types'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { FC, use, useEffect, useMemo, useRef, useState } from 'react'
import SetPasscodeDialog from './SetPasscodeDialog'
import { Button } from '@/lib/components/ui/button'
import passwordRoundedIcon from '@iconify-icons/material-symbols/password-rounded'
import { Icon } from '@iconify/react'
import { Loader2, LockKeyholeOpen, X } from 'lucide-react'
import ImageItem from '@/lib/components/ImageItem'
import heartIcon from '@iconify-icons/mdi/heart'
import heartOffIcon from '@iconify-icons/mdi/heart-off'
import UnlockAlbumDialog from './UnlockAlbumDialog'
import { AxiosError } from 'axios'
import editRoundedIcon from '@iconify-icons/material-symbols/edit-rounded'
import keyRoundedIcon from '@iconify-icons/material-symbols/key-rounded'
import settingsRoundedIcon from '@iconify-icons/material-symbols/settings-rounded'
import { cn } from '@/lib/utils'
import { imageFallbackUrl } from '@/lib/constants'
import { toast } from 'sonner'
import EmptyImageList from '@/lib/components/EmptyImageList'
import ChangePasscodeDialog from './ChangePasscodeDialog'
import SettingDialog from './SettingDialog'
import shieldLockRoundedIcon from '@iconify-icons/material-symbols/shield-lock-rounded'
import globeIcon from '@iconify-icons/mdi/globe'
import globeOffIcon from '@iconify-icons/mdi/globe-off'
import swapHorizRounedIcon from '@iconify-icons/material-symbols/swap-horiz-rounded'
import { useImagesQuery } from '@/lib/hooks/use-images-query'
import ImageViewer from '@/lib/components/image-viewer/ImageViewer'

const ViewAlbumPage: FC<{ params: Promise<{ id: string }> }> = ({ params }) => {
  const { id: albumId } = use(params)
  const [selectedImageIds, setSelectedImageIds] = useState<number[]>([])
  const [inSelectMode, setInSelectMode] = useState(false)
  const [newImages, setNewImages] = useState<CustomerImage[]>([])
  const lastUpdated = useAtomValue(lastUpdatedAtom)
  const albumPasscode = useAtomValue(albumPasscodeAtom)
  const setLastUpdated = useSetAtom(lastUpdatedAtom)
  const enteredPasscode = albumPasscode[albumId]
  const [isEditOptionShown, setIsEditOptionShown] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [rejectSetPasscodeAlbumIds, setRejectSetPasscodeAlbumIds] = useAtom(rejectSetPasscodeAlbumIdsAtom)
  const [isSetPasscodeDialogOpen, setIsSetPasscodeDialogOpen] = useState(false)
  const setPasswordOnOpen = useRef(false)
  const user = useAtomValue(userAtom)
  const {
    data: albumInfo,
    error: albumInfoError,
    isFetching: isFetchingAlbumInfo,
    refetch: refetchAlbumInfo,
  } = useQuery({
    queryKey: ['albumCustomerView:info', albumId, enteredPasscode, lastUpdated[`album:${albumId}`]],
    retry: false,
    placeholderData: keepPreviousData,
    queryFn: async () => {
      const res = await axiosInstance.get<{ customer_info: Album }>(`/customer/album/info/${albumId}`, {
        headers: {
          passcode: enteredPasscode || undefined,
        },
      })

      if (
        res.data?.customer_info.is_passcode_protected &&
        enteredPasscode &&
        !res.data?.customer_info.is_customer_auth
      ) {
        toast.error('Mật khẩu không chính xác')
      }

      return res.data?.customer_info
    },
  })
  const failedByUnauthorized = albumInfoError instanceof AxiosError && albumInfoError.status === 400
  const {
    data: albumData,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    queryParams,
    onFilterChange,
  } = useImagesQuery(
    {
      albumId,
      enteredPasscode,
    },
    ['albumCustomerView:images', lastUpdated[`album:${albumId}`]],
  )
  const albumDataFlattened = useRef<CustomerImage[]>([])
  const albumDataDict = useMemo(() => {
    const dict: Record<number, CustomerImage> = {}
    const flattened = albumData?.pages.flatMap((x) => x.data) ?? []

    for (const x of flattened) {
      dict[x.id] = x
    }

    for (let i = newImages.length - 1; i >= 0; i--) {
      const image = newImages[i]
      if (dict[image.id]) continue
      dict[image.id] = image
      flattened.unshift(image)
    }

    albumDataFlattened.current = flattened

    return dict
  }, [albumData, newImages])
  const isSelectedImagesPicked: boolean | 'mixed' = useMemo(() => {
    let value: boolean | 'mixed' = false
    const count: [number, number] = [0, 0]

    for (const id of selectedImageIds) {
      count[Number(!!albumDataDict[id].is_ticked)] += 1
      if (count[Number(!albumDataDict[id].is_ticked)] > 0) {
        value = 'mixed'
        break
      } else {
        value = !!albumDataDict[id].is_ticked
      }
    }

    return value
  }, [selectedImageIds, albumDataDict])
  const isSelectedImagesPublic: boolean | 'mixed' = useMemo(() => {
    let value: boolean | 'mixed' = false
    const count: [number, number] = [0, 0]

    for (const id of selectedImageIds) {
      count[Number(!!albumDataDict[id].is_public)] += 1
      if (count[Number(!albumDataDict[id].is_public)] > 0) {
        value = 'mixed'
        break
      } else {
        value = !!albumDataDict[id].is_public
      }
    }

    return value
  }, [selectedImageIds, albumDataDict])
  const isAlbumOwner = !!(albumInfo && user && albumInfo.ptg === user.username)
  const { data: albumToken } = useQuery({
    queryKey: ['albumCustomerView:token', albumInfo?.code, albumInfo?.is_public, isAlbumOwner, enteredPasscode],
    retry: false,
    placeholderData: keepPreviousData,
    queryFn: async () => {
      if (!albumInfo?.code || albumInfo?.is_public || (!enteredPasscode && !isAlbumOwner)) return ''
      const res = await axiosInstance.post(`/customer/album/mercure_jwt/${albumInfo?.code}`, {
        passcode: enteredPasscode,
      })

      return res.data?.token ?? ''
    },
  })
  const clearSelectedImages = () => {
    setSelectedImageIds([])
  }

  const handleTick = () => {
    if (!albumInfo) return
    if (selectedImageIds.length > albumInfo?.limit_image) {
      toast.error(`Bạn chỉ có thể thả tym tối đa ${albumInfo?.limit_image} ảnh`)
      return
    }

    setIsLoading(true)
    toast.promise(
      axiosInstance
        .put(`/customer/album/pick/${albumId}`, {
          set_tym: selectedImageIds.filter((x) => !albumDataDict[x].is_ticked),
          unset_tym: selectedImageIds.filter((x) => albumDataDict[x].is_ticked),
          passcode: enteredPasscode,
        })
        .then(() => {
          setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
          return 'Đã lưu thay đổi'
        })
        .catch((err) => {
          if (err instanceof AxiosError) {
            if (err?.code === 'ERR_NETWORK') {
              throw 'Lỗi mạng, vui lòng thử lại sau'
            } else if (err.code === 'ERR_BAD_REQUEST') {
              throw err.response?.data.error.message || 'Thông tin không hợp lệ'
            }
          } else {
            console.error(err)
            throw 'Lỗi không xác định'
          }
        })
        .finally(() => {
          setIsLoading(false)
          clearSelectedImages()
        }),
      {
        loading: 'Đang lưu thay đổi...',
        success: (msg) => msg ?? '',
        error: (msg) => msg,
      },
    )
  }

  const handleSetVisibility = () => {
    if (!albumInfo) return

    const promises: Promise<never>[] = []
    const privateImageIds = selectedImageIds.filter((x) => !albumDataDict[x].is_public)
    const publicImageIds = selectedImageIds.filter((x) => albumDataDict[x].is_public)

    if (privateImageIds.length > 0) {
      promises.push(
        axiosInstance.put(`/customer/album/set_image_visibility/${albumId}`, {
          image_id: privateImageIds,
          visibility: 'public',
          passcode: enteredPasscode,
        }),
      )
    }

    if (publicImageIds.length > 0) {
      promises.push(
        axiosInstance.put(`/customer/album/set_image_visibility/${albumId}`, {
          image_id: publicImageIds,
          visibility: 'private',
          passcode: enteredPasscode,
        }),
      )
    }

    setIsLoading(true)
    toast.promise(
      Promise.all(promises)
        .then(() => {
          setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
          return 'Thay đổi thành công'
        })
        .finally(() => {
          setIsLoading(false)
          clearSelectedImages()
        }),
      {
        loading: 'Đang lưu thay đổi...',
        success: (msg) => msg,
        error: (msg) => msg,
      },
    )
  }

  useEffect(() => {
    if (!albumInfo?.code || (!albumInfo.is_public && !albumToken)) return
    const url = new URL('https://home.ligmailcompany.com/.well-known/mercure')
    const params = !albumInfo.is_public ? `?magic=${albumToken || ''}` : ''
    url.searchParams.append('topic', `https://chimto.com/image/update/${albumInfo.code}${params}`)

    const eventSource = new EventSource(url)
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data)
      setNewImages((x) => [
        {
          id: data.id,
          imageName: data.image.name,
          uploaded_at: data.successDate.date,
          is_ticked: false,
          image_width: data.image.width,
          image_height: data.image.height,
          imageOptimized: {
            imageNameOptimized: data.image_thumb.name,
            uploaded_at: data.successDate.date,
            image_width: data.image_thumb.width,
            image_height: data.image_thumb.height,
          },
          is_public: true,
          fullPath: imageFallbackUrl + data.image_thumb.name,
          imageTags: [],
          albumCode: albumInfo.code,
          is_interacted: false,
        },
        ...x,
      ])
    }

    return () => {
      eventSource.close()
    }
  }, [albumInfo?.code, albumInfo?.is_public, albumToken])

  useEffect(() => {
    if (albumInfo && !albumInfo.is_passcode_protected) {
      if (!rejectSetPasscodeAlbumIds.includes(albumId)) {
        setIsSetPasscodeDialogOpen(true)
      }
    } else {
      setIsSetPasscodeDialogOpen(false)
    }
  }, [albumInfo, rejectSetPasscodeAlbumIds, albumId])

  useEffect(() => {
    if (selectedImageIds.length === 0 && inSelectMode) {
      setInSelectMode(false)
    }
  }, [inSelectMode, selectedImageIds])

  return (
    <div className="relative h-full">
      <div
        className="bg-secondary p-2 rounded-full flex gap-2 w-fit absolute left-4 right-4 bottom-4 z-1 mx-auto sm:mr-0 transition-all duration-400"
        style={{
          opacity: selectedImageIds.length > 0 ? 1 : 0,
          pointerEvents: selectedImageIds.length > 0 ? 'auto' : 'none',
          transform: selectedImageIds.length > 0 ? 'translateY(0)' : 'translateY(50px)',
        }}
      >
        <Button onClick={clearSelectedImages} size="icon" variant="destructive" className="rounded-full size-10">
          <X className="size-5" />
        </Button>
        <Button variant="default" size="lg" className="rounded-full" onClick={handleTick} disabled={isLoading}>
          {isSelectedImagesPicked === 'mixed' ? (
            <>
              <Icon icon={heartIcon} className="size-5" />
              <Icon icon={swapHorizRounedIcon} className="size-5 mx-2" />
              <Icon icon={heartOffIcon} className="size-5" />
            </>
          ) : isSelectedImagesPicked ? (
            <>
              <Icon icon={heartOffIcon} className="size-5" />
              <span className="-mb-0.5">Huỷ thả tym</span>
            </>
          ) : (
            <>
              <Icon icon={heartIcon} className="size-5" />
              <span className="-mb-0.5">Thả tym</span>
            </>
          )}
        </Button>
        <Button variant="default" size="lg" className="rounded-full" onClick={handleSetVisibility} disabled={isLoading}>
          {isSelectedImagesPublic === 'mixed' ? (
            <>
              <Icon icon={globeIcon} className="size-5" />
              <Icon icon={swapHorizRounedIcon} className="size-5 mx-2" />
              <Icon icon={globeOffIcon} className="size-5" />
            </>
          ) : isSelectedImagesPublic ? (
            <>
              <Icon icon={globeOffIcon} className="size-5" />
              <span className="-mb-0.5">Set riêng tư</span>
            </>
          ) : (
            <>
              <Icon icon={globeIcon} className="size-5" />
              <span className="-mb-0.5">Set công khai</span>
            </>
          )}
        </Button>
      </div>
      <div
        className="flex gap-2 w-fit absolute left-4 right-4 bottom-6 z-1 ml-auto transition-[opacity,transform] duration-400"
        style={{
          opacity: selectedImageIds.length <= 0 ? 1 : 0,
          pointerEvents: selectedImageIds.length <= 0 ? 'auto' : 'none',
          transform: selectedImageIds.length <= 0 ? 'translateY(0)' : 'translateY(-50px)',
        }}
      >
        {!isFetchingAlbumInfo && albumInfo && !albumInfo.is_passcode_protected && (
          <Button
            className="rounded-full"
            onClick={() => {
              setPasswordOnOpen.current = true
              setIsSetPasscodeDialogOpen(true)
            }}
          >
            <Icon icon={passwordRoundedIcon} />
            <span>Đặt mật khẩu</span>
          </Button>
        )}
        <div className="relative">
          <SettingDialog
            albumId={albumId}
            isAlbumPublic={!!albumInfo?.is_public}
            isNamePublic={!!albumInfo?.is_public_name}
            passcode={enteredPasscode}
            albumLastUpdate={albumInfo?.last_updated_at ?? ''}
          >
            {!isFetchingAlbumInfo && albumInfo && albumInfo.is_customer_auth && (
              <Button
                className={cn(
                  'rounded-full absolute right-0 duration-400 -z-1',
                  isEditOptionShown ? 'opacity-100 bottom-[calc(200%+1rem)]' : 'opacity-0 bottom-0 pointer-events-none',
                )}
              >
                <Icon icon={settingsRoundedIcon} />
                Thiết lập album
              </Button>
            )}
          </SettingDialog>
          <ChangePasscodeDialog albumId={albumId}>
            {!isFetchingAlbumInfo && albumInfo && albumInfo.is_customer_auth && (
              <Button
                className={cn(
                  'rounded-full absolute right-0 duration-400 -z-1',
                  isEditOptionShown
                    ? 'opacity-100 bottom-[calc(100%+0.5rem)]'
                    : 'opacity-0 bottom-0 pointer-events-none',
                )}
              >
                <Icon icon={keyRoundedIcon} />
                Đổi mật khẩu
              </Button>
            )}
          </ChangePasscodeDialog>
          {!isFetchingAlbumInfo && albumInfo && albumInfo.is_customer_auth && (
            <Button
              size="icon"
              variant={isEditOptionShown ? 'secondary' : 'default'}
              className="rounded-full relative"
              onClick={() => setIsEditOptionShown((x) => !x)}
            >
              <Icon icon={editRoundedIcon} />
            </Button>
          )}
        </div>
        <UnlockAlbumDialog albumId={albumId} refetch={refetchAlbumInfo}>
          {!isFetchingAlbumInfo &&
            (failedByUnauthorized || (albumInfo?.is_passcode_protected && !albumInfo.is_customer_auth)) && (
              <Button id="open-album-passcode-dialog" className="rounded-full" hidden={failedByUnauthorized}>
                <LockKeyholeOpen />
                <span className="pt-0.5">Mở khoá album</span>
              </Button>
            )}
        </UnlockAlbumDialog>
        {isFetchingAlbumInfo && (
          <div className="size-9 flex items-center justify-center">
            <Loader2 className="animate-spin" />
          </div>
        )}
      </div>
      {!albumInfo?.is_passcode_protected && (
        <SetPasscodeDialog
          albumId={albumId}
          open={isSetPasscodeDialogOpen}
          setPasswordOnOpen={setPasswordOnOpen.current}
          onClose={() => setIsSetPasscodeDialogOpen(false)}
          onReject={() => setRejectSetPasscodeAlbumIds((x) => [...x, albumId])}
        />
      )}
      {!isFetchingAlbumInfo && failedByUnauthorized && (
        <div className="size-full flex flex-col items-center justify-center">
          <Icon icon={shieldLockRoundedIcon} className="size-32 text-accent" />
          <p className="my-4">Album này là riêng tư, vui lòng nhập mật khẩu để mở khoá</p>
          <Button
            className="rounded-full mt-2"
            onClick={() => document.getElementById('open-album-passcode-dialog')?.click()}
          >
            <Icon icon={keyRoundedIcon} />
            <span className="pt-0.5">Nhập mật khẩu</span>
          </Button>
        </div>
      )}
      {albumInfo && albumDataFlattened.current.length === 0 && <EmptyImageList className="absolute" />}
      <Container className="h-full" fullWidth>
        <div className="py-4 px-responsive space-y-4">
          <AlbumMasonry
            data={albumDataFlattened.current}
            isFetching={isFetchingNextPage}
            onScrollBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage()
              }
            }}
            sort={queryParams?.sort}
            privacy={queryParams?.privacy}
            time={queryParams?.time}
            onFilterChange={onFilterChange}
            isAuth={albumInfo?.is_customer_auth || user?.username === albumInfo?.ptg}
          >
            {(imageData) => {
              const isSelected = selectedImageIds.includes(imageData.id)

              return (
                <div className="relative size-full">
                  <ImageItem
                    key={imageData.id}
                    selectable={albumInfo?.is_customer_auth}
                    isShowBadge={albumInfo?.is_customer_auth}
                    imageData={imageData}
                    albumId={albumId}
                    isSelected={isSelected}
                    inSelectMode={inSelectMode}
                    onSelectBtnClick={() => {
                      if (isSelected) {
                        setSelectedImageIds((ids: number[]) => ids.filter((id) => id !== imageData.id))
                      } else {
                        setSelectedImageIds((ids: number[]) => [...ids, imageData.id])
                      }
                    }}
                    onWrapperLongPress={() => {
                      if (!inSelectMode && albumInfo?.is_customer_auth) {
                        if (!isSelected) {
                          setSelectedImageIds((x) => [...x, imageData.id])
                        }
                        setInSelectMode(true)
                      }
                    }}
                    onWrapperClick={() => {
                      if (inSelectMode) {
                        if (isSelected) {
                          setSelectedImageIds((ids: number[]) => ids.filter((id) => id !== imageData.id))
                        } else {
                          setSelectedImageIds((ids: number[]) => [...ids, imageData.id])
                        }
                      }
                    }}
                  />
                </div>
              )
            }}
          </AlbumMasonry>
        </div>
      </Container>
      <ImageViewer isEditable={albumInfo?.is_customer_auth} />
    </div>
  )
}

export default ViewAlbumPage
