'use client'

import { axiosInstance } from '@/lib/axios'
import { Button } from '@/lib/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import { changePasscodeSchema, ChangePasscodeSchemaType } from '@/lib/zod-schema/set-passcode-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { FC, useState } from 'react'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/lib/components/ui/form'
import { Input } from '@/lib/components/ui/input'
import { LoaderCircle } from 'lucide-react'
import { useSetAtom } from 'jotai'
import { albumPasscodeAtom, lastUpdatedAtom } from '@/lib/atoms'
import { toast } from 'sonner'
import { AxiosError } from 'axios'

interface ChangePasscodeDialogProps {
  albumId: string
  children: React.ReactNode
}

const ChangePasscodeDialog: FC<ChangePasscodeDialogProps> = ({ albumId, children }) => {
  const setLastUpdated = useSetAtom(lastUpdatedAtom)
  const [loading, setLoading] = useState(false)
  const setAlbumPasscode = useSetAtom(albumPasscodeAtom)
  const [open, setOpen] = useState(false)
  const form = useForm<ChangePasscodeSchemaType>({
    defaultValues: {
      oldPasscode: '',
      passcode: '',
      confirmPasscode: '',
    },
    resolver: zodResolver(changePasscodeSchema),
  })

  function onSubmit(values: ChangePasscodeSchemaType) {
    setLoading(true)
    axiosInstance
      .put(`/customer/album/passcode_setting/${albumId}`, {
        passcode: values.oldPasscode,
        newpasscode: values.passcode,
      })
      .then((res) => {
        setAlbumPasscode((x) => ({ ...x, [albumId]: values.passcode }))
        setLastUpdated((prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
        setOpen(false)
        toast.success(res.data.message || `Đổi mật khẩu cho album ${albumId} thành công`)
        setTimeout(() => {
          form.reset()
        }, 200)
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin đổi mật khẩu không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (loading) return
        if (open) {
          setOpen(true)
        } else {
          setOpen(false)
          setTimeout(() => {
            setLoading(false)
            form.reset()
          }, 200)
        }
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-secondary focus-visible:outline-0">
        <DialogHeader>
          <DialogTitle>Đổi mật khẩu</DialogTitle>
          <DialogDescription>Nhập mật khẩu cũ và mật khẩu mới</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form className="w-full max-w-sm py-4 space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              disabled={loading}
              control={form.control}
              name="oldPasscode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mật khẩu cũ</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              disabled={loading}
              control={form.control}
              name="passcode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mật khẩu mới</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              disabled={loading}
              control={form.control}
              name="confirmPasscode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Xác nhận mật khẩu mới</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button type="submit" size="lg" className="translate-y-4 rounded-full" disabled={loading}>
                {loading && <LoaderCircle className="animate-spin" />}
                Xác nhận
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default ChangePasscodeDialog
