'use client'

import { axiosInstance } from '@/lib/axios'
import { But<PERSON>, buttonVariants } from '@/lib/components/ui/button'
import { Icon } from '@iconify/react'
import { FC, use, useEffect, useMemo, useRef, useState } from 'react'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import Link from 'next/link'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { addingTagImagesAtom, isQRCodeOpenAtom, lastUpdatedAtom, selectModeEffect, userAtom } from '@/lib/atoms'
import { imageBaseUrl } from '@/lib/constants'
import ImageItem from '@/lib/components/ImageItem'
import { EllipsisVertical, List, LoaderCircle, QrCode, Trash2, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import NextImage from 'next/image'
import { toast } from 'sonner'
import { AxiosError } from 'axios'
import Container from '@/lib/components/Container'
import cloudUploadIcon from '@iconify-icons/material-symbols/cloud-upload'
import cloudSyncIcon from '@iconify-icons/material-symbols/cloud-sync'
import tagOutlineIcon from '@iconify-icons/mdi/tag-outline'
import EmptyImageList from '@/lib/components/EmptyImageList'
import AlbumMasonry from '@/lib/components/AlbumMasonry'
import { useImagesQuery } from '@/lib/hooks/use-images-query'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/lib/components/ui/dropdown-menu'
import AddingTagDialog from '@/lib/components/AddingTagDialog'
import GetImageNamesDialog from './GetImageNamesDialog'
import ImageViewer from '@/lib/components/image-viewer/ImageViewer'
import { useMagicToken } from '@/lib/hooks/use-magic-token'

const AlbumPage: FC<{ params: Promise<{ id: string }> }> = ({ params }) => {
  const getImageNamesDialogTriggerEl = useRef<HTMLButtonElement>(null)
  const lastUpdated = useAtomValue(lastUpdatedAtom)
  const setAddingTagImages = useSetAtom(addingTagImagesAtom)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const user = useAtomValue(userAtom)
  const setIsQRCodeOpen = useSetAtom(isQRCodeOpenAtom)
  const { id: albumId } = use(params)
  const albumLastUpdated = lastUpdated[`album:${albumId}`]
  const [selectedImageIds, setSelectedImageIds] = useState<number[]>([])
  const [inSelectMode, setInSelectMode] = useState(false)
  const {
    data: albumData,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    queryParams,
    onFilterChange,
  } = useImagesQuery(
    {
      albumId,
      isPtgView: true,
    },
    ['albumManage:images', user?.username, albumLastUpdated],
  )
  const albumDataFlattened = useMemo(() => albumData?.pages.flatMap((x) => x.data) ?? [], [albumData])

  const clearSelectedImages = () => {
    setSelectedImageIds([])
  }

  const deleteImages = async () => {
    setIsDeleting(true)
    axiosInstance
      .delete(`/image/delete/${albumId}`, { data: selectedImageIds })
      .then((res) => {
        toast.success(res.data.message || 'Đăng ký thành công')
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Xảy ra lỗi khi xoá ảnh')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setIsDeleting(false)
        setDeleteDialogOpen(false)
        clearSelectedImages()
        refetch()
      })
  }
  const albumToken = useMagicToken({
    albumCode: albumId,
  })
  console.log(albumToken)

  useAtom(selectModeEffect)

  useEffect(() => {
    if (selectedImageIds.length === 0) {
      setDeleteDialogOpen(false)
      if (inSelectMode) {
        setInSelectMode(false)
      }
    }
  }, [inSelectMode, selectedImageIds])

  useEffect(() => {
    return () => {
      setSelectedImageIds([])
    }
  }, [setSelectedImageIds])

  useEffect(clearSelectedImages, [albumLastUpdated])

  return (
    <>
      <div
        className="bg-secondary p-2 rounded-full flex gap-2 w-fit absolute left-4 right-4 bottom-4 z-1 mx-auto sm:mr-0 transition-all duration-400"
        style={{
          opacity: selectedImageIds.length > 0 ? 1 : 0,
          pointerEvents: selectedImageIds.length > 0 ? 'auto' : 'none',
          transform: selectedImageIds.length > 0 ? 'translateY(0)' : 'translateY(50px)',
        }}
      >
        <Button onClick={clearSelectedImages} size="icon" variant="destructive" className="rounded-full size-10">
          <X className="size-5" />
        </Button>
        <Button
          variant="default"
          size="lg"
          className="rounded-full"
          onClick={() => {
            setAddingTagImages({
              albumId,
              images: albumDataFlattened.filter((x) => selectedImageIds.includes(x.id)),
            })
          }}
        >
          <Icon icon={tagOutlineIcon} className="size-5" />
          <span className="-mb-0.5">Sửa tag</span>
        </Button>
        <Dialog
          open={deleteDialogOpen}
          onOpenChange={(open) => {
            if (!isDeleting) {
              setDeleteDialogOpen(open)
            }
          }}
        >
          <DialogTrigger asChild>
            <Button variant="default" size="lg" className="rounded-full">
              <Trash2 className="size-5" />
              <span className="-mb-0.5">Xoá {selectedImageIds.length} ảnh đã chọn</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Xoá ảnh</DialogTitle>
              <DialogDescription>
                Bạn có chắc chắn muốn xoá {selectedImageIds.length} ảnh đã chọn không?
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-4 gap-4 max-h-64 overflow-y-auto">
              {selectedImageIds
                .map((x) => albumDataFlattened.find((y) => y.id === x))
                .filter((x) => x != null)
                .map((x) => (
                  <NextImage
                    key={x.id}
                    src={`${imageBaseUrl}${x?.imageOptimized.imageNameOptimized}`}
                    alt={x?.imageOptimized.imageNameOptimized}
                    width={x.imageOptimized.image_width}
                    height={x.imageOptimized.image_height}
                    className="w-full aspect-square object-contain"
                  />
                ))}
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">Huỷ</Button>
              </DialogClose>
              <Button disabled={isDeleting} onClick={deleteImages}>
                {isDeleting ? <LoaderCircle className="animate-spin" /> : <Trash2 />}
                Xoá
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      {albumDataFlattened.length === 0 && <EmptyImageList className="absolute" />}
      <Container className="h-full">
        <div className="py-4 px-responsive space-y-4">
          <AlbumMasonry
            isPtgView
            data={albumDataFlattened}
            onScrollBottom={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage()
              }
            }}
            isFetching={isFetchingNextPage}
            sort={queryParams?.sort}
            privacy={queryParams?.privacy}
            time={queryParams?.time}
            onFilterChange={onFilterChange}
            isAuth={true}
            toolbar={
              <div className="flex gap-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="rounded-full">
                      <Icon icon="material-symbols:add-photo-alternate-rounded" />
                      <span className="hidden sm:block">Thêm ảnh</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Chọn chế độ upload</DialogTitle>
                      <DialogDescription>Vui lòng chọn chế độ upload file để tiếp tục.</DialogDescription>
                    </DialogHeader>
                    <div className="grid grid-cols-2 gap-4 py-4">
                      <Link
                        href={`/albums/${albumId}/manual-upload`}
                        className={buttonVariants({
                          variant: 'secondary',
                          className: 'h-full flex-col justify-start py-4',
                        })}
                      >
                        <Icon icon={cloudUploadIcon} className="size-16" />
                        <div className="flex flex-col justify-center items-center flex-1">
                          <p className="text-xs text-wrap text-center">Upload theo kiểu truyền thống</p>
                          <p className="text-xs text-wrap text-center">(kéo thả, chọn file)</p>
                        </div>
                      </Link>
                      <Link
                        href={`/albums/${albumId}/auto-sync`}
                        className={buttonVariants({
                          variant: 'secondary',
                          className: cn([
                            'h-full flex-col justify-start py-4',
                            typeof FileSystemObserver === 'undefined' && 'opacity-50 pointer-events-none',
                          ]),
                        })}
                      >
                        <Icon icon={cloudSyncIcon} className="size-16" />
                        <div className="flex flex-col justify-center items-center flex-1">
                          <p className="text-xs text-wrap text-center">Chế độ đồng bộ hoá</p>
                        </div>
                      </Link>
                      {typeof FileSystemObserver === 'undefined' && (
                        <p className="text-xs text-wrap col-span-2 italic text-destructive">
                          Chế độ đồng bộ hoá không khả dụng trên thiết bị hiện tại.
                        </p>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button className="rounded-full" size="icon" variant="secondary">
                      <EllipsisVertical />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent side="top" align="end">
                    <DropdownMenuItem className="cursor-pointer" onClick={() => setIsQRCodeOpen(true)}>
                      <QrCode className="text-inherit" />
                      Hiện QR
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => getImageNamesDialogTriggerEl.current?.click()}
                    >
                      <List className="text-inherit" />
                      Lấy danh sách tên file
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            }
          >
            {(imageData) => {
              const isSelected = selectedImageIds.includes(imageData.id)

              return (
                <ImageItem
                  key={imageData.id}
                  isTagEditable
                  isShowBadge={false}
                  imageData={imageData}
                  albumId={albumId}
                  isSelected={isSelected}
                  inSelectMode={inSelectMode}
                  onSelectBtnClick={() => {
                    if (isSelected) {
                      setSelectedImageIds((ids: number[]) => ids.filter((id) => id !== imageData.id))
                    } else {
                      setSelectedImageIds((ids: number[]) => [...ids, imageData.id])
                    }
                  }}
                  onWrapperLongPress={() => {
                    if (!inSelectMode) {
                      if (!isSelected) {
                        setSelectedImageIds((x) => [...x, imageData.id])
                      }
                      setInSelectMode(true)
                    }
                  }}
                  onWrapperClick={() => {
                    if (inSelectMode) {
                      if (isSelected) {
                        setSelectedImageIds((ids: number[]) => ids.filter((id) => id !== imageData.id))
                      } else {
                        setSelectedImageIds((ids: number[]) => [...ids, imageData.id])
                      }
                    }
                  }}
                />
              )
            }}
          </AlbumMasonry>
        </div>
      </Container>
      <AddingTagDialog />
      <GetImageNamesDialog albumId={albumId} selectedImageIds={selectedImageIds}>
        <button type="button" ref={getImageNamesDialogTriggerEl} className="hidden"></button>
      </GetImageNamesDialog>
      <ImageViewer isEditable />
    </>
  )
}

export default AlbumPage
