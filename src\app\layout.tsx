import type { Metadata } from 'next'
import { JetBrains_Mono } from 'next/font/google'
import localFont from 'next/font/local'
import './globals.css'
import { FC, Suspense } from 'react'
import { ThemeProvider } from '@/lib/components/ThemeProvider'
import SidebarLeft from './Sidebar'
import Header from './Header'
import QueryProvider from '@/app/query-provider'
import NextTopLoader from 'nextjs-toploader'
import TokenWatcher from '@/lib/components/client-components/TokenWatcher'
import ScreenSizeWatcher from '@/lib/components/client-components/ScreenSizeWatcher'
import { Toaster } from '@/lib/components/ui/sonner'
import JotaiProvider from '@/lib/components/client-components/JotaiProvider'
import AlbumSearch from '@/lib/components/AlbumSearch'

const inter = localFont({
  src: '../../public/fonts/InterVariable.woff2',
  variable: '--font-inter',
  display: 'swap',
})

const jetbrainsMono = JetBrains_Mono({
  variable: '--font-jetbrains-mono',
  subsets: ['latin'],
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Lalacos Teto',
}

const RootLayout: FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${jetbrainsMono.variable} antialiased`}>
        <JotaiProvider>
          <TokenWatcher />
          <ScreenSizeWatcher />
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
            <QueryProvider>
              <div className="h-body mt-14 sm:ml-18 overflow-hidden">{children}</div>
              <Header />
              <Suspense>
                <SidebarLeft />
              </Suspense>
              <NextTopLoader color="var(--primary)" showSpinner={false} height={1} />
              <Toaster position="top-center" />
              <AlbumSearch />
            </QueryProvider>
          </ThemeProvider>
        </JotaiProvider>
      </body>
    </html>
  )
}

export default RootLayout
