'use client'

import { FC, useMemo, useState } from 'react'
import { ColumnDef, createColumnHelper } from '@tanstack/react-table'
import { DataTable } from './data-table'
import { ImageTag } from '@/lib/types'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { editTagAtom, isTagDialogOpenAtom, lastUpdatedAtom, userAtom } from '@/lib/atoms'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { axiosInstance } from '@/lib/axios'
import './page.css'
import { Button } from '@/lib/components/ui/button'
import { ArrowDown, ArrowUp, LoaderCircle, MoreHorizontal, Plus, Trash2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/lib/components/ui/dropdown-menu'
import editRoundedIcon from '@iconify-icons/material-symbols/edit-rounded'
import { Icon } from '@iconify/react'
import Tag from '@/lib/components/Tag'
import { format } from 'date-fns'
import { ScrollArea, ScrollBar } from '@/lib/components/ui/scroll-area'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/lib/components/ui/alert-dialog'
import { toast } from 'sonner'
import { AxiosError } from 'axios'

const columnHelper = createColumnHelper<ImageTag>()

const TagsPage: FC = () => {
  const [tagToDelete, setTagToDelete] = useState<ImageTag | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isDeleteTagOpen, setIsDeleteTagOpen] = useState(false)
  const user = useAtomValue(userAtom)
  const setIsTagDialogOpen = useSetAtom(isTagDialogOpenAtom)
  const setEditTag = useSetAtom(editTagAtom)
  const [lastUpdated, setLastUpdated] = useAtom(lastUpdatedAtom)
  const { data: tags } = useQuery({
    queryKey: ['tags', user?.username, lastUpdated['tags']],
    placeholderData: keepPreviousData,
    queryFn: async ({ signal }) => {
      const res = await axiosInstance.get('/ptg/tags/list', { signal })

      if (res.status === 200) {
        return res.data.tags_list as ImageTag[]
      }

      return []
    },
  })
  const columns: ColumnDef<ImageTag, string>[] = useMemo(
    () => [
      columnHelper.display({
        id: 'tag',
        header: 'Tag',
        cell: ({ row }) => <Tag tag={row.original} />,
      }),
      columnHelper.accessor('name', {
        header: ({ column }) => {
          return (
            <Button
              variant="ghost"
              className="h-auto py-1.5 font-semibold -ml-3 transition-none"
              onClick={() => {
                column.toggleSorting()
              }}
            >
              Tên
              {column.getIsSorted() === 'asc' && <ArrowUp />}
              {column.getIsSorted() === 'desc' && <ArrowDown />}
            </Button>
          )
        },
      }),
      columnHelper.accessor('color', {
        header: ({ column }) => {
          return (
            <Button
              variant="ghost"
              className="h-auto py-1.5 font-semibold -ml-3 transition-none"
              onClick={() => {
                column.toggleSorting()
              }}
            >
              Màu
              {column.getIsSorted() === 'asc' && <ArrowUp />}
              {column.getIsSorted() === 'desc' && <ArrowDown />}
            </Button>
          )
        },
        cell: ({ getValue }) => <span className="uppercase">#{getValue()}</span>,
      }),
      columnHelper.accessor('created_at', {
        header: ({ column }) => {
          return (
            <Button
              variant="ghost"
              className="h-auto py-1.5 font-semibold -ml-3 transition-none"
              onClick={() => {
                column.toggleSorting()
              }}
            >
              Thời gian tạo
              {column.getIsSorted() === 'asc' && <ArrowUp />}
              {column.getIsSorted() === 'desc' && <ArrowDown />}
            </Button>
          )
        },
        cell: ({ getValue }) => format(getValue(), 'dd/MM/yyyy HH:mm:ss'),
      }),
      columnHelper.display({
        id: 'actions',
        cell: ({ row }) => (
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="size-8">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => {
                  setEditTag(row.original)
                  setIsTagDialogOpen(true)
                }}
              >
                <Icon icon={editRoundedIcon} className="text-inherit" />
                Sửa
              </DropdownMenuItem>
              <DropdownMenuItem
                variant="destructive"
                className="cursor-pointer"
                onClick={() => {
                  setTagToDelete(row.original)
                  setIsDeleteTagOpen(true)
                }}
              >
                <Trash2 className="text-inherit" />
                Xoá
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
      }),
    ],
    [setEditTag, setIsTagDialogOpen],
  )

  function handleDeleteTag(tagId: number) {
    setIsDeleting(true)
    axiosInstance
      .delete('/ptg/tags/delete', { data: { id: [tagId] } })
      .then(() => {
        setLastUpdated((prev) => ({ ...prev, ['tags']: Date.now() }))
        toast.success('Xoá tag thành công')
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err.code === 'ERR_BAD_REQUEST') {
            toast.error(err.response?.data.error.message || 'Thông tin thiết lập không hợp lệ')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setIsDeleting(false)
        setIsDeleteTagOpen(false)
      })
  }

  return (
    <>
      <ScrollArea className="h-full">
        <div className="mx-auto relative max-w-5xl py-4 px-responsive space-y-4">
          <div className="flex justify-between">
            <span className="text-2xl font-semibold">Danh sách tag gán ảnh</span>
            <Button onClick={() => setIsTagDialogOpen(true)}>
              <Plus />
              Thêm tag
            </Button>
          </div>
          <DataTable columns={columns} data={tags} />
        </div>
        <ScrollBar />
      </ScrollArea>
      <AlertDialog
        open={isDeleteTagOpen}
        onOpenChange={(open) => {
          if (!isDeleting) {
            setIsDeleteTagOpen(open)
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận</AlertDialogTitle>
            <AlertDialogDescription>Bạn có chắc chắn muốn xoá tag này?</AlertDialogDescription>
          </AlertDialogHeader>
          {tagToDelete && <Tag tag={tagToDelete} />}
          <AlertDialogFooter>
            <AlertDialogCancel>Huỷ</AlertDialogCancel>
            <Button onClick={() => handleDeleteTag(tagToDelete?.id ?? 0)} disabled={isDeleting}>
              {isDeleting && <LoaderCircle className="animate-spin" />}
              Đồng ý
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default TagsPage
