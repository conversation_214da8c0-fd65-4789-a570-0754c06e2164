'use client'

import { FC, useRef } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const QueryProvider: FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = useRef(
    new QueryClient({
      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false,
        },
      },
    }),
  )
  return <QueryClientProvider client={queryClient.current}>{children}</QueryClientProvider>
}

export default QueryProvider
