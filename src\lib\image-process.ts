import { AxiosError } from 'axios'
import { lastUpdatedAtom, store } from './atoms'
import { axiosInstance } from './axios'
import { imageBaseUrl, imageFallbackUrl, imageVariants } from './constants'
import { uploadQueue } from './queues'
import { UploadingImage } from './types'

export async function processImage(
  id: string,
  albumId: string,
  file: File,
  fileName: string,
  update: (data: Partial<UploadingImage>) => void,
  controller: AbortController,
  delay = 0,
) {
  await new Promise((resolve) => setTimeout(resolve, delay))

  if (controller.signal.aborted) {
    update({ state: 'failed', failedReason: 'Đã hủy' })
    return
  }

  const localUrl = URL.createObjectURL(file)
  const image = new Image()

  update({ localUrl, state: 'pending' })

  try {
    if (controller.signal.aborted) {
      update({ state: 'failed', failedReason: 'Đã hủy' })
      return
    }

    await uploadQueue.add(
      () =>
        new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            image.onerror = null
            image.onload = null
            resolve()
          }, 10000)

          image.onerror = () => {
            image.onerror = null
            clearTimeout(timeout)
            const failedReason = 'Đọc file thất bại'
            update({ state: 'failed', failedReason })
            image.src = ''
            reject(failedReason)
          }
          image.onload = () => {
            clearTimeout(timeout)
            image.onerror = null
            image.onload = null
            resolve()
          }
          image.src = localUrl
        }),
      { timeout: 12000, id, signal: controller.signal },
    )

    if (controller.signal.aborted) {
      update({ state: 'failed', failedReason: 'Đã hủy' })
      return
    }

    const promises = imageVariants.map((variant) =>
      uploadQueue.add(
        () => {
          update({
            state: 'compressing',
            originalWidth: image.width,
            originalHeight: image.height,
          })

          return new Promise<[Blob, number, number]>(async (resolve, reject) => {
            let outputWidth = image.width
            let outputHeight = image.height
            const canvas = document.createElement('canvas')

            if (outputWidth * outputHeight > variant.maxResolution) {
              const scale = Math.sqrt(variant.maxResolution / (outputWidth * outputHeight))
              outputWidth = Math.floor(outputWidth * scale)
              outputHeight = Math.floor(outputHeight * scale)
            }

            canvas.width = outputWidth
            canvas.height = outputHeight
            const ctx = canvas.getContext('2d')

            if (ctx) {
              ctx.imageSmoothingQuality = 'high'
              ctx.imageSmoothingEnabled = true
              ctx.drawImage(image, 0, 0, outputWidth, outputHeight)
            }

            canvas.toBlob(
              (blob) => {
                if (blob) {
                  resolve([blob, outputWidth, outputHeight])
                } else {
                  reject(new Error('Failed to convert image to blob'))
                }
              },
              'image/webp',
              variant.quality,
            )
          })
        },
        { id, signal: controller.signal },
      ),
    )
    const data = await Promise.all(promises)

    if (controller.signal.aborted) {
      update({ state: 'failed', failedReason: 'Đã hủy' })
      return
    }

    const [converted, thumbnail] = data as [Blob, number, number][]
    const thumbnailLocalUrl = URL.createObjectURL(thumbnail[0])

    if (controller.signal.aborted) {
      update({ state: 'failed', failedReason: 'Đã hủy' })
      return
    }

    uploadQueue.add(
      async () => {
        const formData = new FormData()
        formData.append('code', albumId)
        formData.append('image', converted[0], fileName)
        formData.append('thumbnail', thumbnail[0], fileName)

        try {
          update({
            state: 'uploading',
            localUrl: thumbnailLocalUrl,
            convertedWidth: converted[1],
            convertedHeight: converted[2],
            thumbnailWidth: thumbnail[1],
            thumbnailHeight: thumbnail[2],
          })

          const { data } = await axiosInstance.post('/image/upload', formData, {
            signal: controller.signal,
          })
          store.set(lastUpdatedAtom, (prev) => ({ ...prev, [`album:${albumId}`]: Date.now() }))
          update({
            state: 'uploaded',
            remoteImageUrl: imageBaseUrl + data.image.name,
            remoteThumbnailUrl: imageBaseUrl + data.image_thumb.name,
            remoteThumbnailFallbackUrl: imageFallbackUrl + data.image_thumb.name,
            uploadedAt: data.successDate,
            localUrl: undefined,
          })
        } catch (error) {
          let failedReason = 'Unknown error'

          if (error instanceof AxiosError) {
            failedReason =
              error.response?.data?.error?.message ??
              error.response?.data.message ??
              error.response?.data.error ??
              failedReason
            console.log(failedReason)
          }

          update({
            state: 'failed',
            failedReason: failedReason,
          })
        } finally {
          URL.revokeObjectURL(thumbnailLocalUrl)
        }
      },
      { id, signal: controller.signal },
    )
  } finally {
    image.src = ''
    URL.revokeObjectURL(localUrl)
  }
}
