import { ComponentProps, FC } from 'react'
import { ImageTag } from '@/lib/types'
import { cn } from '../utils'

interface TagProps extends ComponentProps<'div'> {
  tag: ImageTag
}

const Tag: FC<TagProps> = ({ tag, className, style, ...props }) => {
  return (
    <div
      {...props}
      style={{ borderColor: `#${tag.color}`, ...style }}
      className={cn(
        'h-6 w-fit pointer-events-auto flex items-center justify-center gap-1.5 text-xs border rounded-md bg-background backdrop-blur-md px-1.5 py-1',
        className,
      )}
    >
      <div style={{ backgroundColor: `#${tag.color}` }} className="size-3 rounded-full"></div>
      <span className="translate-y-[1px]">{tag.name}</span>
    </div>
  )
}

export default Tag
