'use client'

import { headerItem<PERSON>tom } from '@/lib/atoms'
import CopyText from '@/lib/components/CopyText'
import { useSetAtom } from 'jotai'
import { FC, useEffect } from 'react'

const ViewAlbumClientLayout: FC<{ children: React.ReactNode; albumId: string }> = ({ children, albumId }) => {
  const setHeaderTitle = useSetAtom(headerItemAtom)

  useEffect(() => {
    setHeaderTitle(
      <CopyText text={albumId}>
        Đang xem album - <span className="font-medium">{albumId}</span>
      </CopyText>,
    )
    return () => {
      setHeaderTitle('')
    }
  }, [setHeaderTitle, albumId])

  return children
}

export default ViewAlbumClientLayout
