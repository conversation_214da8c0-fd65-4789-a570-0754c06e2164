'use client'

import { uploadingImagesAtom } from '@/lib/atoms'
import Container from '@/lib/components/Container'
import { UploadingImage } from '@/lib/types'
import { cn, uuidv4 } from '@/lib/utils'
import { useAtom } from 'jotai'
import { FC, use, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Icon } from '@iconify/react'
import UploadingImageGrid from '@/lib/components/UploadingImageGrid'
import QRCodeButton from '@/lib/components/QRCodeButton'
import { buttonVariants } from '@/lib/components/ui/button'
import { processImage } from '@/lib/image-process'

const ManualUploadPage: FC<{ params: Promise<{ id: string }> }> = ({ params }) => {
  const [uploadingImages, setUploadingImages] = useAtom(uploadingImagesAtom)
  const { id: albumId } = use(params)
  const editUploadingImage = useCallback(
    (id: string, data: Partial<UploadingImage>) => {
      setUploadingImages((prev) => {
        let uploadingImages = prev[albumId] ?? []
        const index = uploadingImages.findIndex((x) => x.id === id)

        if (index != null && index > -1) {
          uploadingImages[index] = {
            ...uploadingImages[index],
            ...data,
          }
          uploadingImages = [...uploadingImages]
        }

        return { ...prev, [albumId]: uploadingImages }
      })
    },
    [setUploadingImages, albumId],
  )
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    useFsAccessApi: true,
    multiple: true,
    accept: {
      'image/*': [],
    },
    onDropAccepted: (acceptedFiles) => {
      const newImages = acceptedFiles.map((file) => {
        const fileName = file.name
        const id = uuidv4()
        const controller = new AbortController()
        const process = processImage.bind(
          null,
          id,
          albumId,
          file,
          fileName,
          editUploadingImage.bind(null, id),
          controller,
        )

        return {
          id,
          file: file,
          fileName,
          state: 'pending',
          process,
          controller,
        } satisfies UploadingImage
      })

      setUploadingImages((prev) => {
        prev[albumId] = prev[albumId] ?? []
        prev[albumId].push(...newImages)

        return { ...prev }
      })

      newImages.forEach((x) => {
        x.process()
      })
    },
  })

  return (
    <>
      <div className="flex gap-2 w-fit absolute left-4 right-4 bottom-6 z-1 mx-auto sm:mr-0 transition-[opacity,transform] duration-400">
        <QRCodeButton />
      </div>
      <Container>
        <div
          className={cn('p-4', uploadingImages[albumId]?.length ? 'h-64' : 'h-body')}
          onClickCapture={(e) => {
            if ((e.target as HTMLElement).tagName !== 'LABEL') {
              e.stopPropagation()
            }
          }}
        >
          <div
            {...getRootProps({
              className: cn(
                'h-full flex flex-col justify-center items-center rounded-md transition-all select-none pb-4',
                isDragActive ? 'bg-secondary/50' : 'bg-transparent',
              ),
            })}
          >
            <input name="image-input" {...getInputProps()} />
            <Icon icon="material-symbols:add-photo-alternate-rounded" className="size-32 text-accent" />
            <p className="font-medium text-neutral-500 dark:text-neutral-400 pointer-events-none text-center">
              {isDragActive ? 'Thả file để bắt đầu tải lên.' : 'Kéo thả ảnh bạn muốn tải lên tại đây.'}
            </p>
            <label
              htmlFor="image-input"
              className={buttonVariants({ className: '!rounded-full !px-6 mt-2 cursor-pointer' })}
            >
              <Icon icon="material-symbols:add-photo-alternate-rounded" />
              Chọn ảnh
            </label>
          </div>
        </div>
        <div className="pb-4 px-4">
          <UploadingImageGrid images={uploadingImages[albumId] ?? []} />
        </div>
      </Container>
    </>
  )
}

export default ManualUploadPage
