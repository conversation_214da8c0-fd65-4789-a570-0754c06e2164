'use client'

import { forwardRef, useEffect, useImperative<PERSON><PERSON>le } from 'react'
import { useEditor, EditorContent, Extension } from '@tiptap/react'
import Document from '@tiptap/extension-document'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'
import Placeholder from '@tiptap/extension-placeholder'

interface EditorProps {
  value?: string
  editable?: boolean
  placeholder?: string
  onChange?: (body: string) => void
  onEnter?: () => void
}

export interface EditorRef {
  focus: () => void
}

// eslint-disable-next-line react/display-name
const Editor = forwardRef<EditorRef, EditorProps>(({ value, onChange, editable = true, placeholder, onEnter }, ref) => {
  const editor = useEditor(
    {
      extensions: [
        Document,
        Paragraph,
        Text,
        Placeholder.configure({
          placeholder,
          emptyEditorClass:
            'cursor-text before:content-[attr(data-placeholder)] before:absolute before:top-0 before:left-0 before:opacity-50 before-pointer-events-none',
        }),
        Extension.create({
          name: 'Keyboard<PERSON>and<PERSON>',
          addKeyboardShortcuts: () => {
            return {
              Enter: ({ editor }) => {
                editor?.commands.blur()
                onEnter?.()
                return true
              },
              'Shift-Enter': ({ editor }) => {
                return editor.commands.first(({ commands }) => [
                  () => commands.newlineInCode(),
                  () => commands.createParagraphNear(),
                  () => commands.liftEmptyBlock(),
                  () => commands.splitBlock(),
                ])
              },
            }
          },
        }),
      ],
      immediatelyRender: false,
      content: value ? JSON.parse(value) : undefined,
      editable,
      onUpdate({ editor }) {
        if (onChange) {
          const value = editor.getJSON()
          onChange?.(editor.isEmpty ? '' : JSON.stringify(value))
        }
      },
    },
    [onChange, editable, placeholder],
  )

  useImperativeHandle(
    ref,
    () => ({
      focus: () => editor?.commands.focus('end', { scrollIntoView: false }),
    }),
    [editor],
  )

  useEffect(() => {
    editor?.commands.setContent(value ? JSON.parse(value) : '', { emitUpdate: false })
  }, [value, editor])

  return <EditorContent className="*:focus-visible:outline-0 relative" editor={editor} />
})

export default Editor
