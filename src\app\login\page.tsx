'use client'

import { FC, useState } from 'react'
import { Button } from '@/lib/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Input } from '@/lib/components/ui/input'
import { useForm } from 'react-hook-form'
import { Form, FormMessage, FormControl, FormLabel, FormItem, FormField } from '@/lib/components/ui/form'
import { LoginFormSchema, LoginFormSchemaType } from '@/lib/zod-schema/auth-schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter, useSearchParams } from 'next/navigation'
import { axiosInstance } from '@/lib/axios'
import { toast } from 'sonner'
import Link from 'next/link'
import { AxiosError } from 'axios'
import { useSetAtom } from 'jotai'
import { userAtom } from '@/lib/atoms'

const LoginPage: FC = () => {
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const setUser = useSetAtom(userAtom)
  const form = useForm<LoginFormSchemaType>({
    defaultValues: {
      username: '',
      password: '',
    },
    resolver: zodResolver(LoginFormSchema),
  })

  function onSubmit(values: LoginFormSchemaType) {
    setLoading(true)
    axiosInstance
      .post('/api/login', values)
      .then((res) => {
        toast.success('Đăng nhập thành công')
        console.log(res)
        setUser(res.data.token || null)
        router.replace(searchParams.get('redirect') || '/')
        form.reset()
      })
      .catch((err) => {
        if (err instanceof AxiosError) {
          if (err?.code === 'ERR_NETWORK') {
            toast.error('Lỗi mạng, vui lòng thử lại sau')
          } else if (err?.code === 'ERR_BAD_REQUEST') {
            toast.error('Tên đăng nhập hoặc mật khẩu không chính xác')
          }
        } else {
          console.log(err)
          toast.error('Lỗi không xác định')
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-56px)] h-0">
      <Form {...form}>
        <form className="w-full max-w-sm p-4" onSubmit={form.handleSubmit(onSubmit)}>
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl text-center">Đăng nhập</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                disabled={loading}
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên đăng nhập</FormLabel>
                    <FormControl>
                      <Input {...field} type="text" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                disabled={loading}
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mật khẩu</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" className="bg-accent shadow-none" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={loading}>
                Đăng nhập
              </Button>
            </CardFooter>
            <p className="text-xs text-center">
              Bạn chưa có tài khoản?{' '}
              <Link href="/register" className="text-primary hover:underline">
                Đăng ký
              </Link>
            </p>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default LoginPage
