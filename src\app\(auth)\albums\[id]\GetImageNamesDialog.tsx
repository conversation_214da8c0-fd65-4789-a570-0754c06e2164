import { axiosInstance } from '@/lib/axios'
import CopyText from '@/lib/components/CopyText'
import { Button } from '@/lib/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import { Label } from '@/lib/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/lib/components/ui/radio-group'
import { Textarea } from '@/lib/components/ui/textarea'
import { ImageName } from '@/lib/types'
import { cn } from '@/lib/utils'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { Download } from 'lucide-react'
import { FC, useMemo, useState } from 'react'

interface GetImageNamesDialogProps {
  albumId: string
  selectedImageIds?: number[]
  children?: React.ReactNode
}

type Format = 'txt' | 'json'
type TxtFormat = 'txt-comma' | 'txt-lf'
type Limit = 'all' | 'picked' | 'selected'

const GetImageNamesDialog: FC<GetImageNamesDialogProps> = ({ albumId, children, selectedImageIds = [] }) => {
  const [open, setOpen] = useState(false)
  const [format, setFormat] = useState<Format>('txt')
  const [txtFormat, setTxtFormat] = useState<TxtFormat>('txt-comma')
  const [limit, setLimit] = useState<Limit>('all')
  const { data } = useQuery({
    queryKey: [albumId, open, limit],
    placeholderData: keepPreviousData,
    initialData: [],
    queryFn: async ({ signal }) => {
      if (!open) return []

      const searchParams = new URLSearchParams()
      if (limit === 'picked') {
        searchParams.set('is_ticked', '1')
      } else if (limit === 'selected' && selectedImageIds.length > 0) {
        searchParams.set('image_id_list', selectedImageIds.join(','))
      }
      const res = await axiosInstance.get(`/customer/album/image/get_og_filename/${albumId}?${searchParams}`, {
        signal,
      })
      if (res.status === 204) return []

      return res.data.customer_image as ImageName[]
    },
  })
  const output = useMemo(() => {
    if (format === 'txt') {
      switch (txtFormat) {
        case 'txt-comma':
          return data.map((x) => x.og_filename).join(',')
        case 'txt-lf':
          return data.map((x) => x.og_filename).join('\n')
      }
    } else if (format === 'json') {
      return JSON.stringify(
        data.map((x) => x.og_filename),
        null,
        2,
      )
    }

    return ''
  }, [data, format, txtFormat])

  const handleDownloadFile = () => {
    const blob = new Blob([output], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${albumId}_image_names.${format}`
    link.click()
    URL.revokeObjectURL(url)
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (open) {
          setFormat('txt')
          setLimit('all')
        }
        setOpen(open)
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-xl focus-visible:outline-0">
        <DialogHeader>
          <DialogTitle>Lấy danh sách tên file</DialogTitle>
          <DialogDescription className="hidden"></DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-2">
          <RadioGroup className="h-fit" onValueChange={(value) => setFormat(value as Format)} value={format}>
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <RadioGroupItem value="txt" id="format-txt" />
                <Label htmlFor="format-txt">.txt</Label>
              </div>
              <RadioGroup
                disabled={format !== 'txt'}
                onValueChange={(value) => setTxtFormat(value as TxtFormat)}
                value={txtFormat}
                className="h-fit"
              >
                <div className="flex items-center gap-3 pl-4">
                  <RadioGroupItem value="txt-comma" id="format-txt-comma" />
                  <Label htmlFor="format-txt-comma" className={cn({ 'text-muted-foreground': format !== 'txt' })}>
                    Cách bởi dấu phẩy
                  </Label>
                </div>
                <div className="flex items-center gap-3 pl-4">
                  <RadioGroupItem value="txt-lf" id="format-txt-lf" />
                  <Label htmlFor="format-txt-lf" className={cn({ 'text-muted-foreground': format !== 'txt' })}>
                    Cách bởi dòng
                  </Label>
                </div>
              </RadioGroup>
            </div>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="json" id="format-json" />
              <Label htmlFor="format-json">.json</Label>
            </div>
          </RadioGroup>
          <RadioGroup className="h-fit" onValueChange={(value) => setLimit(value as Limit)} value={limit}>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="all" id="limit-all" />
              <Label htmlFor="limit-all">Toàn bộ ảnh</Label>
            </div>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="picked" id="limit-picked" />
              <Label htmlFor="limit-picked">Chỉ ảnh khách đã thả tym</Label>
            </div>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="selected" id="limit-selected" disabled={!selectedImageIds.length} />
              <Label htmlFor="limit-selected" className={cn({ 'text-muted-foreground': !selectedImageIds.length })}>
                Chỉ ảnh đã chọn ({selectedImageIds.length})
              </Label>
            </div>
          </RadioGroup>
        </div>
        <div className="pt-2">
          <div className="flex justify-between items-center mb-1">
            <span className="text-lg leading-none font-semibold">Danh sách tên file</span>
            <CopyText text={output} />
          </div>
          <Textarea value={output} className="max-h-48 resize-none" rows={4} />
        </div>
        <Button className="mx-auto" onClick={handleDownloadFile}>
          <Download />
          Tải về
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default GetImageNamesDialog
