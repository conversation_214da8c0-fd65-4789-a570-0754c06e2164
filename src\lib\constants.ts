import { IconifyIcon } from '@iconify/react'
import { UploadingImageState } from './types'
import hourglassTopRoundedIcon from '@iconify-icons/material-symbols/hourglass-top-rounded'
import photoSizeSelectSmallRoundedIcon from '@iconify-icons/material-symbols/photo-size-select-small-rounded'
import cloudUploadIcon from '@iconify-icons/material-symbols/cloud-upload'
import checkCircleRoundedIcon from '@iconify-icons/material-symbols/check-circle-rounded'
import errorRoundedIcon from '@iconify-icons/material-symbols/error-rounded'
import hourglassEmptyRoundedIcon from '@iconify-icons/material-symbols/hourglass-empty-rounded'
import deleteRoundedIcon from '@iconify-icons/material-symbols/delete-rounded'

export const screenSizeBreakpoints = {
  base: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
}
export const uploadingImageStateMap: Record<UploadingImageState, { label: string; color: string; icon: IconifyIcon }> =
  {
    pending: { label: '<PERSON>ang chờ', color: 'bg-slate-300', icon: hourglassTopRoundedIcon },
    loading: { label: '<PERSON>ang tải', color: 'bg-yellow-300', icon: hourglassEmptyRoundedIcon },
    compressing: { label: 'Đang nén', color: 'bg-cyan-300', icon: photoSizeSelectSmallRoundedIcon },
    uploading: { label: 'Đang tải lên', color: 'bg-pink-300', icon: cloudUploadIcon },
    uploaded: { label: 'Đã tải lên', color: 'bg-green-300', icon: checkCircleRoundedIcon },
    failed: { label: 'Thất bại', color: 'bg-red-400', icon: errorRoundedIcon },
    deleted: { label: 'Đã xóa', color: 'bg-gray-500', icon: deleteRoundedIcon },
  }
export const maxImageResolution = 24_000_000
export const maxImageDimension = 10_000
export const maxThumbnailResolution = 250_000
export const maxThumbnailDimension = 720
export const apiBaseUrl = 'https://home.ligmailcompany.com'
export const imageBaseUrl = 'https://cdn-teto.lalacos.moe/'
export const imageFallbackUrl = 'https://home.ligmailcompany.com/images/'

export const imageVariants: {
  quality: number
  maxResolution: number
  maxDimension: number
  type: 'image' | 'thumbnail'
}[] = [
  {
    quality: 0.8,
    maxResolution: maxImageResolution,
    maxDimension: maxImageDimension,
    type: 'image' as const,
  },
  {
    quality: 0.6,
    maxResolution: maxThumbnailResolution,
    maxDimension: maxThumbnailDimension,
    type: 'thumbnail' as const,
  },
]

export const drawToolColors: { label: string; hex: string }[] = [
  { label: 'Đen', hex: '#000000' },
  { label: 'Đỏ', hex: '#f24822' },
  { label: 'Cam', hex: '#ff9e42' },
  { label: 'Vàng', hex: '#ffc943' },
  { label: 'Xanh lá', hex: '#66d575' },
  { label: 'Xanh dương', hex: '#3dadff' },
  { label: 'Tím', hex: '#874fff' },
  { label: 'Trắng', hex: '#ffffff' },
]
