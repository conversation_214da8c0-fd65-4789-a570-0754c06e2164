'use client'

import { But<PERSON> } from '@/lib/components/ui/button'
import { X } from 'lucide-react'
import { FC, useEffect } from 'react'
import { QRCodeSVG } from 'qrcode.react'
import { cn } from '@/lib/utils'
import { useAtom, useAtomValue } from 'jotai'
import { isQRCodeOpenAtom, screenSizeAtom } from '@/lib/atoms'
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from '@/lib/components/ui/drawer'
import { screenSizeBreakpoints } from '@/lib/constants'

const QRPanel: FC<{ albumId: string }> = ({ albumId }) => {
  const [isQRCodeOpen, setIsQRCodeOpen] = useAtom(isQRCodeOpenAtom)
  const screenSize = useAtomValue(screenSizeAtom)
  const isSidePanelOpen = screenSize >= screenSizeBreakpoints.md && isQRCodeOpen
  const isDrawerOpen = screenSize < screenSizeBreakpoints.md && isQRCodeOpen
  const url = new URL(`/view/${albumId}`, location.origin)

  useEffect(() => {
    return () => {
      setIsQRCodeOpen(false)
    }
  }, [setIsQRCodeOpen])

  return (
    <>
      <div
        className={cn(
          'h-full shrink-0 flex justify-center items-center overflow-hidden transition-[width,opacity] duration-400',
          isSidePanelOpen ? 'w-56' : 'w-0',
          isSidePanelOpen ? 'opacity-100' : 'opacity-0',
        )}
      >
        <div className="w-56 shrink-0 border-l h-full p-4 flex flex-col gap-4 justify-center items-center relative">
          <Button variant="secondary" className="absolute top-4 left-4" onClick={() => setIsQRCodeOpen(false)}>
            <X />
            Đóng
          </Button>
          <QRCodeSVG size={180} bgColor="transparent" fgColor="var(--primary)" value={url.toString()} />
          <span className="text-sm text-center">Vui lòng đưa mã QR này cho khách</span>
        </div>
      </div>
      <Drawer
        open={isDrawerOpen}
        onOpenChange={(open) => {
          if (!open && isDrawerOpen) {
            setIsQRCodeOpen(false)
          }
        }}
      >
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle className="text-center">Mã QR album</DrawerTitle>
            <DrawerDescription className="text-center">Vui lòng đưa mã QR này cho khách</DrawerDescription>
          </DrawerHeader>
          <div className="flex justify-center items-center pb-12">
            <QRCodeSVG size={180} bgColor="transparent" fgColor="var(--primary)" value="https://reactjs.org/" />
          </div>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default QRPanel
