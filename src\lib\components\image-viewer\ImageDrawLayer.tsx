import { album<PERSON><PERSON><PERSON><PERSON><PERSON>, brush<PERSON>olor<PERSON>tom, brushSize<PERSON>tom, drawType<PERSON>tom, selectionTool<PERSON>tom, store } from '@/lib/atoms'
import { axiosInstance } from '@/lib/axios'
import { CustomerImage, DrawLine, Point } from '@/lib/types'
import { cn, uuidv4 } from '@/lib/utils'
import { keepPreviousData, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useAtomValue } from 'jotai'
import { getStrokePoints, StrokeOptions } from 'perfect-freehand'
import { simplify } from 'points-on-curve'
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDebounce, useUpdate } from 'react-use'
import { toast } from 'sonner'

interface ImageDrawLayerProps {
  image: CustomerImage
  elTarget: HTMLElement | null
}

const strokeOptions: StrokeOptions = {
  size: 1,
  thinning: 0,
  smoothing: 0.99,
  streamline: 0.99,
}

const ImageDrawLayer: FC<ImageDrawLayerProps> = ({ image, elTarget }) => {
  const update = useUpdate()
  const isDrawingRef = useRef(false)
  const isErasingRef = useRef(false)
  const workingPathRef = useRef<SVGPathElement | null>(null)
  const drawingPointsRef = useRef<Point[]>([])
  const [pendingDrawLines, setPendingDrawLines] = useState<DrawLine[]>([])
  const linesToRemoveRef = useRef<Record<string, true>>({})
  const albumPasscode = useAtomValue(albumPasscodeAtom)
  const enteredPasscode = albumPasscode[image.albumCode]
  const queryClient = useQueryClient()
  const { data } = useQuery({
    queryKey: ['image:draws', image.id, enteredPasscode],
    initialData: [],
    placeholderData: keepPreviousData,
    queryFn: async ({ signal }) => {
      const searchParams = new URLSearchParams()
      searchParams.set('album_cid', image.albumCode)
      searchParams.set('image_id', image.id.toString())

      const res = await axiosInstance.get(`/customer/album/image/interactive/draw?${searchParams}`, {
        signal,
        headers: {
          passcode: enteredPasscode || undefined,
        },
      })

      if (res.status === 204) {
        return []
      }

      res.data.draws_list?.forEach((line: DrawLine) => {
        const pointCount = line.path.length / 2
        const points: [number, number][] = []

        for (let i = 0; i < pointCount; i++) {
          points.push([line.path[i * 2], line.path[i * 2 + 1]])
        }

        line.calculatedPath = ['M', points[0], 'L', points.slice(1)].join(' ')
      })

      return res.data.draws_list as DrawLine[]
    },
  })
  const lines = useMemo(
    () => [...data, ...pendingDrawLines].filter((line) => line.calculatedPath),
    [data, pendingDrawLines],
  )
  const { mutate: addLines } = useMutation({
    mutationFn: async (lines: DrawLine[]) => {
      const res = await axiosInstance.post(
        '/customer/album/image/interactive/draw',
        {
          image_id: image.id,
          album_cid: image.albumCode,
          list: lines.map((line) => ({
            path: line.path,
            color: line.color,
            type: line.type,
            size: line.size,
            temp_id: line.uuid,
          })),
        },
        {
          headers: {
            passcode: enteredPasscode || undefined,
          },
        },
      )
      await new Promise((resolve) => setTimeout(resolve, 2000))
      return res.data.data as DrawLine[]
    },
    onMutate: async (lines) => {
      const ids = lines.map((x) => x.uuid)
      setPendingDrawLines((prev) => prev.filter((line) => !ids.includes(line.uuid)))
      queryClient.setQueryData(['image:draws', image.id, enteredPasscode], (prev: DrawLine[]) => {
        return [...prev, ...lines]
      })
    },
    onSuccess: (newLines, oldLines) => {
      const ids = oldLines.map((x) => x.uuid)

      newLines.forEach((line: DrawLine) => {
        const pointCount = line.path.length / 2
        const points: [number, number][] = []

        for (let i = 0; i < pointCount; i++) {
          points.push([line.path[i * 2], line.path[i * 2 + 1]])
        }

        line.calculatedPath = ['M', points[0], 'L', points.slice(1)].join(' ')
      })
      console.log('old', oldLines)
      console.log('new', newLines)

      queryClient.setQueryData(['image:draws', image.id, enteredPasscode], (prev: DrawLine[]) => {
        return [...prev.filter((line) => !ids.includes(line.uuid)), ...newLines]
      })
    },
    onError: (err, oldLines) => {
      const ids = oldLines.map((x) => x.uuid)
      queryClient.setQueryData(['image:draws', image.id, enteredPasscode], (prev: DrawLine[]) => {
        return prev.filter((line) => !ids.includes(line.uuid))
      })

      if (err instanceof AxiosError) {
        if (err?.code === 'ERR_NETWORK') {
          toast.error('Lỗi mạng, vui lòng thử lại sau')
        } else if (err.code === 'ERR_BAD_REQUEST') {
          toast.error(err.response?.data.error.message || 'Thông tin không hợp lệ')
        }
      } else {
        console.error(err)
        toast.error('Lỗi không xác định')
      }
    },
  })
  const { mutate: removeLines } = useMutation({
    mutationFn: async (lines: DrawLine[]) => {
      const res = await axiosInstance.delete('/customer/album/image/interactive/draw', {
        data: {
          image_id: image.id,
          album_cid: image.albumCode,
          uuids: lines.map((x) => x.uuid),
        },
        headers: {
          passcode: enteredPasscode || undefined,
        },
      })
      return res.data.data.uuids as string[]
    },
    onMutate: async (lines) => {
      const ids = lines.map((x) => x.uuid)
      ids.forEach((id) => {
        delete linesToRemoveRef.current[id]
      })
      update()
      queryClient.setQueryData(['image:draws', image.id, enteredPasscode], (prev: DrawLine[]) => {
        return prev.filter((line) => !ids.includes(line.uuid))
      })
      setPendingDrawLines((prev) => prev.filter((line) => !ids.includes(line.uuid)))
    },
    onError: (err, oldLines) => {
      queryClient.setQueryData(['image:draws', image.id, enteredPasscode], (prev: DrawLine[]) => {
        return [...prev, ...oldLines]
      })

      if (err instanceof AxiosError) {
        if (err?.code === 'ERR_NETWORK') {
          toast.error('Lỗi mạng, vui lòng thử lại sau')
        } else if (err.code === 'ERR_BAD_REQUEST') {
          toast.error(err.response?.data.error.message || 'Thông tin không hợp lệ')
        }
      } else {
        console.error(err)
        toast.error('Lỗi không xác định')
      }
    },
  })

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!elTarget) return
      const rect = elTarget.getBoundingClientRect()
      const currentZoomRatio = rect.width / image.image_width
      const newPoint: Point = {
        x: (1 / currentZoomRatio) * (e.clientX - rect.x),
        y: (1 / currentZoomRatio) * (e.clientY - rect.y),
      }

      drawingPointsRef.current.push(newPoint)
      const points = getStrokePoints(drawingPointsRef.current, strokeOptions).map((x) => x.point)
      const [first, ...rest] = simplify(points as [number, number][], 0.05)

      if (rest.length === 0) {
        rest.push(first)
      }

      const pathData = ['M', first, 'L', rest].join(' ')
      workingPathRef.current?.setAttribute('d', pathData)
    },
    [image, elTarget],
  )

  useDebounce(
    () => {
      if (pendingDrawLines.length === 0) return
      addLines(pendingDrawLines)
    },
    1000,
    [pendingDrawLines],
  )

  // useEffect(() => {
  //   if (!albumInfo?.code || (!albumInfo.is_public && !albumToken)) return
  //   const url = new URL('https://home.ligmailcompany.com/.well-known/mercure')
  //   const params = !albumInfo.is_public ? `?magic=${albumToken || ''}` : ''
  //   url.searchParams.append('topic', `https://chimto.com/image/update/${albumInfo.code}${params}`)

  //   const eventSource = new EventSource(url)
  //   eventSource.onmessage = (event) => {
  //     const data = JSON.parse(event.data)
  //     setNewImages((x) => [
  //       {
  //         id: data.id,
  //         imageName: data.image.name,
  //         uploaded_at: data.successDate.date,
  //         is_ticked: false,
  //         image_width: data.image.width,
  //         image_height: data.image.height,
  //         imageOptimized: {
  //           imageNameOptimized: data.image_thumb.name,
  //           uploaded_at: data.successDate.date,
  //           image_width: data.image_thumb.width,
  //           image_height: data.image_thumb.height,
  //         },
  //         is_public: true,
  //         fullPath: imageFallbackUrl + data.image_thumb.name,
  //         imageTags: [],
  //         albumCode: albumInfo.code,
  //         is_interacted: false,
  //       },
  //       ...x,
  //     ])
  //   }

  //   return () => {
  //     eventSource.close()
  //   }
  // }, [albumInfo?.code, albumInfo?.is_public, albumToken])

  useEffect(() => {
    console.log(123)

    const handleMouseUp = () => {
      if (isDrawingRef.current) {
        isDrawingRef.current = false
        document.removeEventListener('mousemove', handleMouseMove)

        const points = getStrokePoints(drawingPointsRef.current, strokeOptions).map((x) => x.point)
        const [first, ...rest] = simplify(points as [number, number][], 0.05)

        if (rest.length === 0) {
          rest.push(first)
        }

        const pathData = ['M', first, 'L', rest].join(' ')

        workingPathRef.current?.setAttribute('d', '')
        drawingPointsRef.current = []
        setPendingDrawLines((prev) => [
          ...prev,
          {
            uuid: uuidv4(),
            path: [first, ...rest].flatMap((x) => x),
            color: store.get(brushColorAtom),
            type: store.get(drawTypeAtom),
            size: store.get(brushSizeAtom),
            calculatedPath: pathData,
          } satisfies DrawLine,
        ])
      }

      if (isErasingRef.current) {
        isErasingRef.current = false

        let currentLines = queryClient.getQueryData(['image:draws', image.id, enteredPasscode]) as DrawLine[]
        setPendingDrawLines((prev) => {
          currentLines = [...prev, ...currentLines]
          return prev
        })

        const deletingLines = currentLines.filter((x) => linesToRemoveRef.current[x.uuid])
        if (deletingLines.length === 0) return

        removeLines(deletingLines)
      }
    }

    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [enteredPasscode, handleMouseMove, image.id, queryClient, removeLines])

  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => {
      if (!workingPathRef.current) return

      const selectionTool = store.get(selectionToolAtom)
      const drawType = store.get(drawTypeAtom)

      if (selectionTool === 'draw' && e.button === 0) {
        switch (drawType) {
          case 'marker':
          case 'highlight':
            isDrawingRef.current = true
            break
          case 'erase':
            isErasingRef.current = true
            break
        }
      }

      if (isDrawingRef.current) {
        document.addEventListener('mousemove', handleMouseMove)
        const drawType = store.get(drawTypeAtom)
        const brushSize = store.get(brushSizeAtom)
        const brushColorTransparent = drawType === 'highlight' ? '7F' : ''
        const brushColor = store.get(brushColorAtom) + brushColorTransparent
        workingPathRef.current.setAttribute('fill', 'transparent')
        workingPathRef.current.setAttribute('stroke', brushColor)
        workingPathRef.current.setAttribute('stroke-width', brushSize.toString())
        workingPathRef.current.setAttribute('stroke-linecap', drawType === 'highlight' ? 'butt' : 'round')
      }
    }

    elTarget?.addEventListener('mousedown', handleMouseDown)

    return () => {
      elTarget?.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [elTarget, handleMouseMove])

  return (
    <>
      {lines.map((line) => (
        <path
          key={line.uuid}
          d={line.calculatedPath}
          fill="transparent"
          stroke={line.color + (line.type === 'highlight' ? '7F' : '')}
          strokeWidth={line.size}
          strokeLinecap={line.type === 'highlight' ? 'butt' : 'round'}
          className={cn({
            'opacity-40 pointer-events-none': linesToRemoveRef.current[line.uuid],
          })}
          onMouseEnter={() => {
            if (isErasingRef.current) {
              linesToRemoveRef.current[line.uuid] = true
              update()
            }
          }}
        />
      ))}
      <path ref={workingPathRef}></path>
    </>
  )
}

export default ImageDrawLayer
