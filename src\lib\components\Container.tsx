import { ComponentProps, FC } from 'react'
import { ScrollArea, ScrollBar } from './ui/scroll-area'
import { cn } from '../utils'

interface ContainerProps extends ComponentProps<'div'> {
  fullWidth?: boolean
}

const Container: FC<ContainerProps> = ({ children, className, fullWidth, ...props }) => {
  return (
    <ScrollArea className="h-full">
      <div className={cn('mx-auto relative', fullWidth ? 'w-full' : 'container', className)} {...props}>
        {children}
      </div>
      <ScrollBar />
    </ScrollArea>
  )
}

export default Container
