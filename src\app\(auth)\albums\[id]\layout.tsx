import { FC, ReactNode, use } from 'react'
import QRPanel from './QRPanel'
import type { Metadata } from 'next'
import AlbumClientLayout from './AlbumClientLayout'

type Props = {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params

  return {
    title: `Album ${id}`,
  }
}

const AlbumLayout: FC<{ children: ReactNode; params: Promise<{ id: string }> }> = ({ children, params }) => {
  const { id: albumId } = use(params)

  return (
    <div className="h-full flex flex-nowrap">
      <div className="h-full flex-1 relative">
        <AlbumClientLayout albumId={albumId}>{children}</AlbumClientLayout>
      </div>
      <QRPanel albumId={albumId} />
    </div>
  )
}

export default AlbumLayout
