import { Card } from '@/lib/components/ui/card'
import { Album } from '@/lib/types'
import { FC } from 'react'
import EmptyImageList from '@/lib/components/EmptyImageList'
import useEmblaCarousel from 'embla-carousel-react'
import { imageBaseUrl } from '@/lib/constants'
import { buttonVariants } from '@/lib/components/ui/button'
import { BookImage, ChevronRight } from 'lucide-react'
import Link from 'next/link'

interface SearchAlbumCardProps {
  album: Album
}

const SearchAlbumCard: FC<SearchAlbumCardProps> = ({ album }) => {
  const [emblaRef] = useEmblaCarousel({
    dragFree: true,
    containScroll: 'trimSnaps',
  })

  return (
    <Card className="p-2 gap-2 rounded-md bg-accent">
      <Link className={buttonVariants({ variant: 'ghost' })} href={`/view/${album.code}`}>
        <BookImage className="text-foreground" />
        <span className="text-foreground">{album.code}</span>
        <ChevronRight className="ml-auto" />
      </Link>
      {album.thumbnail.length ? (
        <div className="w-full rounded overflow-hidden col-span-2 lg:col-span-1" ref={emblaRef}>
          <div className="flex flex-nowrap gap-2 h-28">
            {album.thumbnail.map((x, index) => (
              <img
                key={index}
                className="h-full aspect-square rounded object-cover"
                src={`${imageBaseUrl}${x.imageOptimized.imageNameOptimized}`}
                alt="ảnh"
              />
            ))}
          </div>
        </div>
      ) : (
        <EmptyImageList className="h-28 col-span-2 lg:col-span-1" />
      )}
    </Card>
  )
}

export default SearchAlbumCard
