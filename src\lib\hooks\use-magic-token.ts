import { albumPasscode<PERSON>tom, userAtom } from '@/lib/atoms'
import { axiosInstance } from '@/lib/axios'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'

export function useMagicToken({ albumCode, canGetToken = true }: { albumCode: string; canGetToken?: boolean }) {
  const albumPasscode = useAtomValue(albumPasscodeAtom)
  const user = useAtomValue(userAtom)
  const enteredPasscode = albumPasscode[albumCode]
  const { data: albumToken } = useQuery({
    queryKey: ['album:token', albumCode, canGetToken, enteredPasscode ?? '', user?.username],
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnMount: false,
    queryFn: async ({ signal }) => {
      if (!albumCode || !canGetToken) return ''
      console.log(albumCode, canGetToken, enteredPasscode ?? '', user?.username)

      const res = await axiosInstance.post(`/customer/album/mercure_jwt/${albumCode}`, {
        passcode: enteredPasscode || undefined,
        signal,
      })

      return (res.data?.token as string) ?? ''
    },
  })

  return albumToken
}
