import { atom, createStore } from 'jotai'
import { atomEffect } from 'jotai-effect'
import { drawToolColors, screenSizeBreakpoints } from '@/lib/constants'
import { CustomerImage, DrawType, ImageAction, ImageTag, Tool, UploadingImage } from '@/lib/types'
import { parseJwt } from '@/lib/utils'
import { ReactNode } from 'react'

export const store = createStore()

export const isMobileSidebarOpenAtom = atom(false)

const tokenPayloadAtom = atom(parseJwt(globalThis.localStorage?.getItem('token') ?? null))

export const userAtom = atom(
  (get) => get(tokenPayloadAtom),
  (_, set, newToken: string | null) => {
    if (newToken) {
      localStorage.setItem('token', newToken)
      set(tokenPayloadAtom, parseJwt(newToken))
    } else {
      localStorage.removeItem('token')
      set(tokenPayloadAtom, null)
    }
  },
)

export const screenSizeAtom = atom(screenSizeBreakpoints.base)

export const inSelectModeAtom = atom(false)

export const selectedAlbumImageIdsAtom = atom<number[]>([])

export const selectModeEffect = atomEffect((get, set) => {
  if (get(inSelectModeAtom) && get(selectedAlbumImageIdsAtom).length === 0) {
    set(inSelectModeAtom, false)
  }
})

export const uploadingImagesAtom = atom<Record<string, UploadingImage[]>>({})
export const syncingImagesAtom = atom<
  Record<
    string,
    Record<
      string,
      {
        handle: FileSystemDirectoryHandle
        images: UploadingImage[]
        observer?: FileSystemObserver
      }
    >
  >
>({})

export const lastUpdatedAtom = atom<Record<string, number>>({})

export const isQRCodeOpenAtom = atom(false)

export const rejectSetPasscodeAlbumIdsAtom = atom<string[]>([])

export const albumPasscodeAtom = atom<Record<string, string>>({})

export const headerItemAtom = atom<ReactNode | null>(null)

export const isTagDialogOpenAtom = atom(false)
export const editTagAtom = atom<ImageTag | null>(null)

export const addingTagImagesAtom = atom<{ albumId: string; images: CustomerImage[] } | null>(null)

export const viewingImageAtom = atom<CustomerImage | null>(null)
export const zoomFactorAtom = atom<{ zoom: number; baseScale: number }>({ zoom: 1, baseScale: 1 })
export const selectionToolAtom = atom<Tool>('move')
export const imageActionAtom = atom<ImageAction | null>(null)
export const drawTypeAtom = atom<DrawType>('marker')
export const markerColorAtom = atom(drawToolColors[0].hex)
export const markerSizeAtom = atom(5)
export const highlighterColorAtom = atom(drawToolColors[0].hex)
export const highlighterSizeAtom = atom(10)
export const brushColorAtom = atom(
  (get) => {
    switch (get(drawTypeAtom)) {
      case 'marker':
        return get(markerColorAtom)
      case 'highlight':
        return get(highlighterColorAtom)
      case 'erase':
        return '#000000'
    }
  },
  (get, set, newColor: string) => {
    switch (get(drawTypeAtom)) {
      case 'marker':
        set(markerColorAtom, newColor)
        break
      case 'highlight':
        set(highlighterColorAtom, newColor)
        break
    }
  },
)
export const brushSizeAtom = atom(
  (get) => {
    switch (get(drawTypeAtom)) {
      case 'marker':
        return get(markerSizeAtom)
      case 'highlight':
        return get(highlighterSizeAtom)
      case 'erase':
        return 1
    }
  },
  (get, set, newSize: number) => {
    switch (get(drawTypeAtom)) {
      case 'marker':
        set(markerSizeAtom, Math.max(0, newSize))
        break
      case 'highlight':
        set(highlighterSizeAtom, Math.max(0, newSize))
        break
    }
  },
)

export const isSearchAlbumOpenAtom = atom(false)
