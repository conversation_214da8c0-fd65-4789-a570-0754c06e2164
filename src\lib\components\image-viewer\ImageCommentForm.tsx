'use no memo'

import { forwardRef, useEffect, useImperativeHandle } from 'react'
import { useForm, UseFormReturn } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { commentSchema, CommentSchemaType } from '@/lib/zod-schema/comment-schema'
import { Form, FormControl, FormField, FormItem } from '@/lib/components/ui/form'
import { CircleArrowUp } from 'lucide-react'
import { Button } from '@/lib/components/ui/button'
import { cn } from '@/lib/utils'
import Editor from './Editor'

interface ImageCommentFormProps {
  focusOnMount?: boolean
  onSubmit?: (values: CommentSchemaType, form: UseFormReturn<CommentSchemaType>) => void
}

export interface ImageCommentFormRef {
  focus: () => void
}

const fallbackOnSubmit = (values: CommentSchemaType) => {
  console.table(values)
}

// eslint-disable-next-line react/display-name
const ImageCommentForm = forwardRef<ImageCommentFormRef, ImageCommentFormProps>(
  ({ onSubmit = fallbackOnSubmit, focusOnMount = false }, ref) => {
    const form = useForm<CommentSchemaType>({
      defaultValues: {
        content: '',
      },
      resolver: zodResolver(commentSchema),
    })
    const content = form.watch('content')

    const handleSubmit = (values: CommentSchemaType) => {
      onSubmit(values, form)
    }

    useEffect(() => {
      if (focusOnMount) {
        setTimeout(() => {
          form.setFocus('content')
        }, 0)
      }
    }, [focusOnMount, form])

    useImperativeHandle(ref, () => ({
      focus: () => {
        form.setFocus('content')
      },
    }))

    return (
      <Form {...form}>
        <form
          className="relative w-fit rounded-md transition-all bg-secondary shadow-lg border border-input has-[.ProseMirror-focused]:border-ring has-[.ProseMirror-focused]:ring-[3px] has-[.ProseMirror-focused]:ring-ring/50"
          onSubmit={form.handleSubmit(handleSubmit)}
        >
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div>
                    <div
                      className={cn('w-64 py-2 px-3 rounded-md shadow-lg resize-none text-sm', {
                        'pb-11 min-h-12': field.value.length > 0,
                        'pr-11': field.value.length === 0,
                      })}
                    >
                      <Editor
                        {...field}
                        placeholder="Nhập nội dung bình luận..."
                        onEnter={() => form.handleSubmit(handleSubmit)()}
                      />
                    </div>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          {content.length === 0 && <CircleArrowUp className="size-4.5 absolute top-2.5 right-2.5 opacity-50" />}
          {content.length > 0 && (
            <div className="absolute left-0 right-0 bottom-0 flex justify-between p-1 border-t border-accent">
              <div></div>
              <Button type={'submit'} variant="ghost" className="size-7 !p-0">
                <CircleArrowUp className="size-4.5" />
              </Button>
            </div>
          )}
        </form>
      </Form>
    )
  },
)

export default ImageCommentForm
