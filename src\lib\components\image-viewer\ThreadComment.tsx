import { ThreadComment } from '@/lib/types'
import { FC, forwardRef } from 'react'
import Editor from './Editor'
import { formatDistanceToNow } from 'date-fns'
import { vi } from 'date-fns/locale/vi'
import UserAvatar from './UserAvatar'

interface ThreadCommentLayoutProps {
  username: string
  children?: React.ReactNode
}

interface ThreadCommentProps {
  username: string
  comment: ThreadComment
}

// eslint-disable-next-line react/display-name
const ThreadCommentLayout = forwardRef<HTMLDivElement | null, ThreadCommentLayoutProps>(
  ({ username, children }, ref) => {
    return (
      <div className="flex" ref={ref}>
        <UserAvatar username={username} className="mt-0.5" />
        {/* <div className="size-6 rounded-full bg-red-500 mt-0.5"></div> */}
        <div>{children}</div>
      </div>
    )
  },
)

const ThreadCommentItem: FC<ThreadCommentProps> = ({ comment, username }) => {
  return (
    <ThreadCommentLayout username={username}>
      <div className="w-64 px-3 -ml-1 text-sm min-h-0">
        <div className="flex mb-1 gap-1.5 items-center h-7">
          <div className="flex gap-1.5 items-baseline">
            <span className="font-bold">{username}</span>
            <span className="text-xs opacity-60">{formatDistanceToNow(comment.created_at, { locale: vi })} trước</span>
          </div>
        </div>
        <Editor value={comment.content} editable={false} />
      </div>
    </ThreadCommentLayout>
  )
}

export { ThreadCommentLayout, ThreadCommentItem }
