'use client'

import { FC, useRef, useState, useCallback, useEffect } from 'react'
import { <PERSON>alog, DialogHeader, DialogOverlay, DialogPortal, DialogTitle } from '@/lib/components/ui/dialog'
import { Content } from '@radix-ui/react-dialog'
import './ImageViewer.css'
import { useAtom, useAtomValue } from 'jotai'
import { drawTypeAtom, selectionToolAtom, viewingImageAtom } from '@/lib/atoms'
import { imageBaseUrl } from '@/lib/constants'
import { cn } from '@/lib/utils'
import ImageToolbar from './ImageToolbar'
import ImageThreadLayer from './ImageThreadLayer'
import ImageDrawLayer from './ImageDrawLayer'

interface ImageViewerProps {
  isEditable?: boolean
}

const minZoom = 0.2
const maxZoom = 5

const ImageViewer: FC<ImageViewerProps> = ({ isEditable }) => {
  const [viewingImage, setViewingImage] = useAtom(viewingImageAtom)
  const [open, setOpen] = useState(!!viewingImage)
  const [viewingImageLocal, setViewingImageLocal] = useState(viewingImage)
  const targetRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const panContainerRef = useRef<HTMLDivElement>(null)
  const svgDrawRef = useRef<SVGSVGElement>(null)
  const zoomRef = useRef(1)
  const positionRef = useRef({ x: 0, y: 0 })
  const isDraggingRef = useRef(false)
  const [selectionTool, setSelectionTool] = useAtom(selectionToolAtom)
  const drawType = useAtomValue(drawTypeAtom)
  const baseScale = useRef(1)

  const setZoom = useCallback(
    (value: number) => {
      zoomRef.current = value
      if (svgDrawRef.current) {
        svgDrawRef.current.style.scale = `${zoomRef.current * baseScale.current}`
      }
      if (targetRef.current && viewingImageLocal) {
        targetRef.current.style.width = `${viewingImageLocal.image_width * baseScale.current * zoomRef.current}px`
        targetRef.current.style.height = `${viewingImageLocal.image_height * baseScale.current * zoomRef.current}px`
      }
    },
    [viewingImageLocal],
  )

  const resetZoomAndPan = useCallback(
    (smooth: boolean = true) => {
      setZoom(1)
      panContainerRef.current?.scrollIntoView({
        behavior: smooth ? 'smooth' : 'instant',
        block: 'center',
        inline: 'center',
      })
    },
    [setZoom],
  )

  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      if (isDraggingRef.current) return

      const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
      const currentZoom = zoomRef.current
      const newZoom = Math.min(Math.max(currentZoom * zoomFactor, minZoom), maxZoom)

      if (targetRef.current && contentRef.current && viewingImageLocal) {
        // Lấy vị trí con trỏ chuột relative với content container
        const contentRect = contentRef.current.getBoundingClientRect()
        const mouseX = e.clientX - contentRect.left
        const mouseY = e.clientY - contentRect.top

        // Lấy scroll position hiện tại
        const scrollLeft = contentRef.current.scrollLeft
        const scrollTop = contentRef.current.scrollTop

        // Tính toán vị trí con trỏ trong coordinate system của content (bao gồm scroll)
        const mouseXInContent = mouseX + scrollLeft
        const mouseYInContent = mouseY + scrollTop

        // Tính toán vị trí con trỏ relative với hình ảnh trước khi zoom
        const imageRect = targetRef.current.getBoundingClientRect()
        const contentRectForImage = contentRef.current.getBoundingClientRect()
        const imageXInContent = imageRect.left - contentRectForImage.left + scrollLeft
        const imageYInContent = imageRect.top - contentRectForImage.top + scrollTop

        const mouseXInImage = mouseXInContent - imageXInContent
        const mouseYInImage = mouseYInContent - imageYInContent

        // Áp dụng zoom mới
        setZoom(newZoom)

        // Tính toán scroll position mới để giữ vị trí con trỏ
        // Vị trí con trỏ trong hình ảnh sau zoom sẽ scale theo tỷ lệ zoom
        const scaleFactor = newZoom / currentZoom
        const newMouseXInImage = mouseXInImage * scaleFactor
        const newMouseYInImage = mouseYInImage * scaleFactor

        // Tính toán scroll position mới
        const newScrollLeft = newMouseXInImage + imageXInContent - mouseX
        const newScrollTop = newMouseYInImage + imageYInContent - mouseY

        // Áp dụng scroll position mới
        contentRef.current.scrollLeft = newScrollLeft
        contentRef.current.scrollTop = newScrollTop
      }
    },
    [setZoom, viewingImageLocal],
  )

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (selectionTool === 'move' || e.button === 1) {
        isDraggingRef.current = true
        positionRef.current = {
          x: e.clientX,
          y: e.clientY,
        }
        if (targetRef.current) {
          targetRef.current.style.cursor = 'grabbing'
        }
      }
    },
    [selectionTool],
  )

  useEffect(() => {
    if (!viewingImageLocal) return

    baseScale.current = Math.min(
      (window.innerWidth - 32) / viewingImageLocal.image_width,
      (window.innerHeight - 160) / viewingImageLocal.image_height,
    )
    setTimeout(() => {
      resetZoomAndPan(false)
    }, 0)

    const handleMouseMove = (e: MouseEvent) => {
      if (isDraggingRef.current) {
        if (isDraggingRef.current && contentRef.current) {
          contentRef.current.scrollLeft -= e.clientX - positionRef.current.x
          contentRef.current.scrollTop -= e.clientY - positionRef.current.y
        }
        positionRef.current = {
          x: e.clientX,
          y: e.clientY,
        }
      }
    }

    const handleMouseUp = () => {
      isDraggingRef.current = false

      if (targetRef.current) {
        targetRef.current.style.cursor = ''
      }
    }

    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault()
      }
    }

    const handleResize = () => {
      baseScale.current = Math.min(
        (window.innerWidth - 32) / viewingImageLocal.image_width,
        (window.innerHeight - 160) / viewingImageLocal.image_height,
      )
      setZoom(zoomRef.current)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('mouseleave', handleMouseUp)
    document.addEventListener('wheel', handleWheel, { passive: false })
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('mouseleave', handleMouseUp)
      document.removeEventListener('wheel', handleWheel)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }, [viewingImageLocal, setZoom, resetZoomAndPan])

  useEffect(() => {
    if (viewingImage) {
      setViewingImageLocal(viewingImage)
      setOpen(true)
      setTimeout(() => {
        resetZoomAndPan(false)
      }, 0)
      setSelectionTool('move')
    } else {
      setOpen(false)
    }
  }, [resetZoomAndPan, setSelectionTool, viewingImage])

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          setViewingImage(null)
        }
      }}
    >
      <DialogPortal>
        <DialogOverlay />
        <Content
          aria-describedby={undefined}
          className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 duration-200 focus-visible:outline-0 !pointer-events-none"
        >
          <DialogHeader className="hidden">
            <DialogTitle></DialogTitle>
          </DialogHeader>
          <div ref={contentRef} className="absolute inset-0 overflow-hidden">
            <div
              ref={panContainerRef}
              className="absolute left-0 top-0 grid-cols-[70vw_auto_70vw] grid-rows-[70vh_auto_70vh] grid size-max select-none"
            >
              <div
                ref={targetRef}
                onMouseDown={handleMouseDown}
                onWheel={handleWheel}
                className={cn('col-start-2 row-start-2 relative bg-white pointer-events-auto', {
                  'cursor-grab': selectionTool === 'move',
                  'cursor-[url(/images/brush-cursor.png)_8_24,auto]': selectionTool === 'draw' && drawType === 'marker',
                  'cursor-[url(/images/highlighter-cursor.png)_8_24,auto]':
                    selectionTool === 'draw' && drawType === 'highlight',
                  'cursor-[url(/images/erase-cursor.png)_8_24,auto]': selectionTool === 'draw' && drawType === 'erase',
                  'cursor-[url(/images/comment-cursor.png)_6_28,auto]': selectionTool === 'comment',
                })}
              >
                <div className="checkered absolute inset-0"></div>
                <img
                  src={viewingImageLocal?.fullPath || `${imageBaseUrl}${viewingImageLocal?.imageName}`}
                  alt={viewingImageLocal?.imageName}
                  className="size-full object-cover relative"
                  draggable={false}
                  onContextMenu={(e) => {
                    e.preventDefault()
                  }}
                />
                {isEditable && (
                  <>
                    <svg
                      ref={svgDrawRef}
                      className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                      width={viewingImageLocal?.image_width}
                      height={viewingImageLocal?.image_height}
                      style={{ touchAction: 'none' }}
                    >
                      <ImageDrawLayer image={viewingImageLocal!} elTarget={targetRef.current} />
                    </svg>
                    <ImageThreadLayer image={viewingImageLocal!} elTarget={targetRef.current} />
                  </>
                )}
              </div>
            </div>
          </div>
          {isEditable && <ImageToolbar resetZoomAndPan={resetZoomAndPan} />}
        </Content>
      </DialogPortal>
    </Dialog>
  )
}

export default ImageViewer
