'use client'

import { But<PERSON> } from '@/lib/components/ui/button'
import { useAtomValue, useSetAtom } from 'jotai'
import { Menu } from 'lucide-react'
import { headerItemAtom, isMobileSidebarOpenAtom } from '@/lib/atoms'
import SwitchThemeButton from '@/lib/components/SwitchThemeButton'
import LalacosLogo from '@public/images/lalacos_logo.svg'
import Link from 'next/link'
import { cn } from '@/lib/utils'

const Header: React.FC = () => {
  const setIsMobileSidebarOpen = useSetAtom(isMobileSidebarOpenAtom)
  const headerItem = useAtomValue(headerItemAtom)

  return (
    <div className="h-14 fixed top-0 left-0 sm:left-18 right-0 bg-background border-b">
      <div className="h-full container mx-auto flex justify-between items-center gap-2 px-responsive">
        <Button variant="secondary" className="size-10 sm:hidden" onClick={() => setIsMobileSidebarOpen(true)}>
          <Menu className="size-6" />
        </Button>
        <div className="flex-1 flex justify-center">
          <Link href="/" className={cn(headerItem ? 'hidden sm:block' : 'block')}>
            <LalacosLogo className="h-6 fill-primary" />
          </Link>
          {headerItem && <div className="mr-auto sm:mr-0 sm:ml-auto">{headerItem}</div>}
        </div>
        <div className="sm:hidden">
          <SwitchThemeButton />
        </div>
      </div>
    </div>
  )
}

export default Header
