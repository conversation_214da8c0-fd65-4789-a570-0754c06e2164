import CopyText from '@/lib/components/CopyText'
import { Separator } from '@/lib/components/ui/separator'
import { Album } from '@/lib/types'
import { format } from 'date-fns'
import { FC } from 'react'

const AlbumInfo: FC<{ album: Album }> = ({ album }) => {
  return (
    <div className="px-4 py-2 bg-accent rounded-md grid grid-cols-[auto_1fr] gap-2 items-center h-full">
      <div className="w-48 sm:w-52 text-card-foreground/80">Tên khách hàng</div>
      <div className="flex-1 text-end font-medium">{album.customer_name}</div>
      <Separator className="col-span-2" />
      <div className="w-48 sm:w-52 text-card-foreground/80">Album ID</div>
      <div className="flex-1 font-medium flex justify-end items-center gap-1">
        <CopyText text={album.code}>{album.code}</CopyText>
      </div>
      <Separator className="col-span-2" />
      <div className="w-48 sm:w-52 text-card-foreground/80">Ngày tạo</div>
      <div className="flex-1 text-end font-medium">{format(album.created_at, 'HH:mm dd/MM/yyyy')}</div>
      <Separator className="col-span-2" />
      <div className="w-48 sm:w-52 text-card-foreground/80">Deadline chọn ảnh</div>
      <div className="flex-1 text-end font-medium">{format(album.end_date, 'HH:mm dd/MM/yyyy')}</div>
      <Separator className="col-span-2" />
      <div className="w-48 sm:w-52 text-card-foreground/80">Số ảnh khách được thả tym</div>
      <div className="flex-1 text-end font-medium">{album.limit_image}</div>
    </div>
  )
}

export default AlbumInfo
