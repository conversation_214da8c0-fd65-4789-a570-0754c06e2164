import { albumPasscode<PERSON>tom, selectionTool<PERSON>tom, store } from '@/lib/atoms'
import { CustomerImage, Thread } from '@/lib/types'
import { FC, useEffect, useState } from 'react'
import ImageCommentForm from './ImageCommentForm'
import { CommentSchemaType } from '@/lib/zod-schema/comment-schema'
import { toast } from 'sonner'
import { axiosInstance } from '@/lib/axios'
import { AxiosError } from 'axios'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import ImageThread from './ImageThread'
import { useAtomValue } from 'jotai'

interface ImageThreadLayerProps {
  image: CustomerImage
  elTarget: HTMLElement | null
}

const ImageThreadLayer: FC<ImageThreadLayerProps> = ({ image, elTarget }) => {
  const [newCommentOffsetPercent, setNewCommentOffsetPercent] = useState<{ left: number; top: number } | null>(null)
  const [openingThread, setOpeningThread] = useState<string | null>(null)
  const albumPasscode = useAtomValue(albumPasscodeAtom)
  const enteredPasscode = albumPasscode[image.albumCode]
  const { data, refetch } = useQuery({
    queryKey: ['image:threads', image.id, enteredPasscode],
    initialData: [],
    placeholderData: keepPreviousData,
    queryFn: async ({ signal }) => {
      const searchParams = new URLSearchParams()
      searchParams.set('album_cid', image.albumCode)
      searchParams.set('image_id', image.id.toString())

      const res = await axiosInstance.get(`/customer/album/image/interactive/thread?${searchParams}`, {
        signal,
        headers: {
          passcode: enteredPasscode || undefined,
        },
      })

      if (res.status === 204) {
        return []
      }

      res.data.threads_list?.forEach((thread: Thread) => {
        thread.imageId = image.id
        thread.albumCode = image.albumCode
        thread.left = (thread.coordinate_x / image.image_width) * 100
        thread.top = (thread.coordinate_y / image.image_height) * 100
      })

      return res.data.threads_list as Thread[]
    },
  })

  const handleNewCommentSubmit = (values: CommentSchemaType) => {
    if (!newCommentOffsetPercent) return

    toast.promise(
      axiosInstance
        .post(
          '/customer/album/image/interactive/thread',
          {
            image_id: image.id,
            album_cid: image.albumCode,
            coordinate_x: (newCommentOffsetPercent.left * image.image_width) / 100,
            coordinate_y: (newCommentOffsetPercent.top * image.image_height) / 100,
            content: values.content,
          },
          {
            headers: {
              passcode: enteredPasscode || undefined,
            },
          },
        )
        .then(() => {
          setNewCommentOffsetPercent(null)
          refetch()
          return 'Tạo thread thành công'
        })
        .catch((err) => {
          if (err instanceof AxiosError) {
            if (err?.code === 'ERR_NETWORK') {
              throw 'Lỗi mạng, vui lòng thử lại sau'
            } else if (err.code === 'ERR_BAD_REQUEST') {
              throw err.response?.data.error.message || 'Thông tin không hợp lệ'
            }
          } else {
            console.error(err)
            throw 'Lỗi không xác định'
          }
        }),
      {
        loading: 'Đang tạo thread...',
        success: (msg) => msg ?? '',
        error: (msg) => msg,
      },
    )
  }

  useEffect(() => {
    if (!elTarget) return

    const handleMouseDown = (e: MouseEvent) => {
      const selectionTool = store.get(selectionToolAtom)

      if (
        selectionTool === 'comment' &&
        e.button === 0 &&
        !(e.target as Element).closest('.comment-detail,.comment-quick-view')
      ) {
        setNewCommentOffsetPercent((x) => {
          if (x || openingThread) {
            setOpeningThread(null)
            return x
          }

          const rect = elTarget.getBoundingClientRect()
          const left = (e.clientX - rect.left) / rect.width
          const top = (e.clientY - rect.top) / rect.height
          e.stopPropagation()

          return { left: left * 100, top: top * 100 }
        })
      }
    }

    elTarget.addEventListener('mousedown', handleMouseDown)

    return () => {
      elTarget.removeEventListener('mousedown', handleMouseDown)
    }
  }, [elTarget, openingThread])

  useEffect(() => {
    setNewCommentOffsetPercent(null)
  }, [image])

  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => {
      if (!(e.target as HTMLElement).closest('.new-comment')) {
        setNewCommentOffsetPercent(null)
      }
      if (!(e.target as Element).closest('.comment-detail,.comment-quick-view')) {
        setOpeningThread(null)
      }
      e.stopPropagation()
    }

    document.addEventListener('mousedown', handleMouseDown)

    return () => {
      document.removeEventListener('mousedown', handleMouseDown)
    }
  }, [])

  return (
    <div>
      {data.map((thread) => (
        <ImageThread
          key={thread.uuid}
          thread={thread}
          isViewDetail={openingThread === thread.uuid}
          onOpenChange={(open) => {
            if (newCommentOffsetPercent) {
              setNewCommentOffsetPercent(null)
            } else if (openingThread || !open) {
              setOpeningThread(null)
            } else {
              setOpeningThread(thread.uuid)
            }
          }}
          className={openingThread && openingThread !== thread.uuid ? 'opacity-40 pointer-events-none' : ''}
        />
      ))}
      {!!newCommentOffsetPercent && (
        <div
          className="new-comment size-0 absolute pointer-events-auto"
          style={{ left: `${newCommentOffsetPercent.left}%`, top: `${newCommentOffsetPercent.top}%` }}
        >
          <div className="-mt-9 w-fit flex gap-2">
            <div className="bg-primary size-9 rounded-[18px] rounded-bl-xs shadow-lg"></div>
            <ImageCommentForm onSubmit={handleNewCommentSubmit} focusOnMount />
          </div>
        </div>
      )}
    </div>
  )
}

export default ImageThreadLayer
