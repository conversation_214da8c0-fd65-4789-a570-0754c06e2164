import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { TokenPayload } from '@/lib/types'
import { isAfter } from 'date-fns'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function parseJwt(token: string | null) {
  if (!token) return null
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      globalThis
        .atob(base64)
        .split('')
        .map((c) => {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join(''),
    )
    const payload = JSON.parse(jsonPayload) as TokenPayload

    if (isAfter(new Date(), payload.exp * 1000)) return null

    return payload
  } catch (error) {
    console.error(error)
    return null
  }
}

export const uuidv4 = () => {
  let d = new Date().getTime()
  let d2 = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0 //Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16 //random number between 0 and 16
    if (d > 0) {
      //Use timestamp until depleted
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
    } else {
      //Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0
      d2 = Math.floor(d2 / 16)
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
}

export function parseSearchParams(searchParams: URLSearchParams) {
  const keyGrouped = [...searchParams.entries()].reduce(
    (prev, cur) => ((prev[cur[0]] || (prev[cur[0]] = [])).push(cur), prev),
    {} as Record<string, [string, string][]>,
  )
  return Object.fromEntries(
    Object.values(keyGrouped)
      .filter((x) => x?.length)
      .map((x) => {
        if (x!.length === 1) {
          return x![0]
        }
        return [x![0][0], x!.map((x) => x[1])]
      }),
  ) as Record<string, string | string[]>
}

function med(A: number[], B: number[]) {
  return [(A[0] + B[0]) / 2, (A[1] + B[1]) / 2]
}

const TO_FIXED_PRECISION = /(\s?[A-Z]?,?-?[0-9]*\.[0-9]{0,2})(([0-9]|e|-)*)/g

export function getSvgPathFromStroke(points: number[][]): string {
  if (!points.length) {
    return ''
  }

  const max = points.length - 1

  return points
    .reduce(
      (acc, point, i, arr) => {
        if (i === max) {
          acc.push(point, med(point, arr[0]), 'L', arr[0], 'Z')
        } else {
          acc.push(point, med(point, arr[i + 1]))
        }
        return acc
      },
      ['M', points[0], 'Q'],
    )
    .join(' ')
    .replace(TO_FIXED_PRECISION, '$1')
}

/** HSL → #RRGGBB  */
function hslToHex(h: number, s: number, l: number): string {
  s /= 100
  l /= 100

  const k = (n: number) => (n + h / 30) % 12
  const a = s * Math.min(l, 1 - l)
  const f = (n: number) => Math.round(255 * (l - a * Math.max(-1, Math.min(k(n) - 3, Math.min(9 - k(n), 1)))))

  const toHex = (x: number) => x.toString(16).padStart(2, '0')
  return `#${toHex(f(0))}${toHex(f(8))}${toHex(f(4))}`
}

const clamp = (v: number, min: number, max: number) => Math.min(Math.max(v, min), max)

/**
 * Sinh màu theo chuỗi, cho phép dao động S & L trong biên độ nhỏ.
 *
 * @param str        Chuỗi nguồn
 * @param satBase    Saturation trung tâm (%). Mặc định 60
 * @param satDelta   Biên độ ± cho Saturation (%). Mặc định 10  → 50–70 %
 * @param lightBase  Lightness trung tâm (%). Mặc định 65
 * @param lightDelta Biên độ ± cho Lightness (%). Mặc định 5   → 80–90 %
 */
export function stringToColor(str: string, satBase = 60, satDelta = 10, lightBase = 65, lightDelta = 5): string {
  /** 1) Tạo hash nguyên */
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }

  /** 2) Hue cố định từ hash */
  const hue = Math.abs(hash) % 360

  /** 3) Lấy thêm bit hash để "lắc" S & L */
  const satVar = ((hash >> 8) & 0xff) / 0xff // 0–1
  const lightVar = ((hash >> 16) & 0xff) / 0xff // 0–1

  const saturation = clamp(satBase - satDelta + satVar * satDelta * 2, 0, 100)
  const lightness = clamp(lightBase - lightDelta + lightVar * lightDelta * 2, 0, 100)

  return hslToHex(hue, saturation, lightness)
}
