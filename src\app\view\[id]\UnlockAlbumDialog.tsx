'use client'
import { But<PERSON> } from '@/lib/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/lib/components/ui/dialog'
import { zodResolver } from '@hookform/resolvers/zod'
import { FC, useState } from 'react'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/lib/components/ui/form'
import { Input } from '@/lib/components/ui/input'
import { LockKeyholeOpen } from 'lucide-react'
import { unlockAlbumSchema, UnlockAlbumSchemaType } from '@/lib/zod-schema/unlock-album-schema'
import { useAtom } from 'jotai'
import { albumPasscodeAtom } from '@/lib/atoms'

interface UnlockAlbumDialogProps {
  albumId: string
  children?: React.ReactNode
  refetch?: () => void
}

const UnlockAlbumDialog: FC<UnlockAlbumDialogProps> = ({ albumId, children, refetch }) => {
  const [albumPasscode, setAlbumPasscode] = useAtom(albumPasscodeAtom)
  const [open, setOpen] = useState(false)
  const form = useForm<UnlockAlbumSchemaType>({
    defaultValues: {
      passcode: '',
    },
    resolver: zodResolver(unlockAlbumSchema),
  })

  function onSubmit(values: UnlockAlbumSchemaType) {
    setOpen(false)
    setAlbumPasscode((x) => ({ ...x, [albumId]: values.passcode }))
    if (albumPasscode[albumId] === values.passcode) {
      refetch?.()
    }
    setTimeout(() => {
      form.reset()
    }, 200)
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open)
        if (!open) {
          setTimeout(() => {
            form.reset()
          }, 200)
        }
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-secondary focus-visible:outline-0">
        <DialogHeader>
          <DialogTitle>Mở khóa album</DialogTitle>
          <DialogDescription className="hidden">Nhập mật khẩu để mở khóa album</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="passcode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mật khẩu</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" className="bg-accent shadow-none" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button type="submit" size="lg" className="mt-4 rounded-full">
                <LockKeyholeOpen />
                <span className="pt-0.5">Mở khoá</span>
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UnlockAlbumDialog
