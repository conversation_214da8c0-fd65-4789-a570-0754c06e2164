import { CSSProperties, FC } from 'react'
import { uploadingImageStateMap } from '../constants'
import { Skeleton } from './ui/skeleton'
import { UploadingImage } from '../types'
import { cn } from '../utils'
import { format } from 'date-fns'
import { Icon } from '@iconify/react'
import errorRoundedIcon from '@iconify-icons/material-symbols/error-rounded'
import syncRoundedIcon from '@iconify-icons/material-symbols/sync-rounded'

const UploadingImageGrid: FC<{ images: UploadingImage[] }> = ({ images }) => {
  return (
    <div
      style={{ display: images.length ? 'grid' : 'none' }}
      className="grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-responsive"
    >
      {images?.map((x) => (
        <div key={x.id} className="rounded-md">
          <div className="aspect-square relative flex justify-center items-center rounded-sm overflow-hidden group hover:cursor-pointer z-1">
            {(x.localUrl || x.remoteThumbnailUrl) && x.originalHeight && x.originalWidth && (
              <div
                style={
                  {
                    '--zoom-square-scale':
                      Math.min(x.originalWidth, x.originalHeight) / Math.max(x.originalWidth, x.originalHeight),
                    aspectRatio: x.originalWidth / x.originalHeight,
                  } as CSSProperties
                }
                className={cn(
                  'transition-all group-hover:scale-(--zoom-square-scale)',
                  x.originalWidth > x.originalHeight ? 'h-full' : 'w-full',
                )}
              >
                <object type="image/webp" data={x.localUrl || x.remoteThumbnailUrl} className="rounded-sm size-full">
                  <img src={x.localUrl || x.remoteThumbnailFallbackUrl} alt={x.file.name} className="rounded-sm" />
                </object>
              </div>
            )}
            {true && (
              <div className="absolute inset-0 flex flex-col justify-center items-center">
                <div className="absolute top-1/2 left-0 right-0 bottom-0 bg-gradient-to-t from-black to-transparent pointer-events-none"></div>
                {['failed', 'deleted'].includes(x.state) && (
                  <>
                    <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50"></div>
                    <div className="absolute w-full top-0 left-0 flex justify-baseline gap-1 p-1 text-white">
                      <Icon icon={errorRoundedIcon} className="shrink-0" />
                      <p className="text-xs text-wrap pt-0.5">{x.failedReason}</p>
                    </div>
                    {x.state === 'failed' && (
                      <div
                        className="flex justify-center items-center opacity-70 hover:scale-110 hover:opacity-100 transition-all duration-300"
                        onClick={x.process}
                      >
                        <Icon icon={syncRoundedIcon} className="size-20" />
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
            {x.state !== 'failed' && !((x.localUrl || x.remoteThumbnailUrl) && x.originalHeight && x.originalWidth) && (
              <Skeleton className="size-full" />
            )}
            <div className="absolute left-0 bottom-0 p-2 text-white">
              <p className="font-semibold text-wrap">{x.file.name}</p>
              {x.uploadedAt && (
                <p className="text-xs opacity-60">{format(new Date(x.uploadedAt), 'dd/MM/yyyy HH:mm')}</p>
              )}
            </div>
          </div>
          <div
            className={cn(
              'text-sm py-0.5 rounded mt-1 relative flex justify-end items-center px-3 gap-1 text-black',
              uploadingImageStateMap[x.state].color,
            )}
          >
            <Icon icon={uploadingImageStateMap[x.state].icon} />
            <span className="font-medium -mb-0.5 py-0.5">{uploadingImageStateMap[x.state].label}</span>
          </div>
        </div>
      ))}
    </div>
  )
}

export default UploadingImageGrid
