'use client'

import EmptyImageList from '@/lib/components/EmptyImageList'
import { Button } from '@/lib/components/ui/button'
import { Card } from '@/lib/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/lib/components/ui/dropdown-menu'
import { imageBaseUrl } from '@/lib/constants'
import { Album } from '@/lib/types'
import useEmblaCarousel from 'embla-carousel-react'
import { BookImage, ChevronDown, ImageIcon, Trash2 } from 'lucide-react'
import Link from 'next/link'
import AlbumInfo from './AlbumInfo'
import AlbumInfoDialog from '@/lib/components/AlbumInfoDialog'
import editRoundedIcon from '@iconify-icons/material-symbols/edit-rounded'
import { Icon } from '@iconify/react'
import { useRef } from 'react'

interface AlbumCardProps {
  album: Album
  onDelete?: () => void
}

const AlbumCard: React.FC<AlbumCardProps> = ({ album, onDelete }) => {
  const albumInfoDialogTrigger = useRef<HTMLButtonElement>(null)
  const [emblaRef] = useEmblaCarousel({
    dragFree: true,
    containScroll: 'trimSnaps',
  })

  return (
    <Card className="p-2 grid grid-cols-[auto_1fr] gap-2">
      <div className="col-span-2 lg:col-span-1 lg:w-104">
        <AlbumInfo album={album} />
      </div>
      {album.thumbnail.length ? (
        <div className="w-full rounded overflow-hidden col-span-2 lg:col-span-1" ref={emblaRef}>
          <div className="flex flex-nowrap gap-2 h-32 sm:h-40 md:h-48 lg:h-52">
            {album.thumbnail.map((x, index) => (
              <img
                key={index}
                className="h-full aspect-square rounded object-cover"
                src={`${imageBaseUrl}${x.imageOptimized.imageNameOptimized}`}
                alt="ảnh"
              />
            ))}
          </div>
        </div>
      ) : (
        <EmptyImageList className="h-32 sm:h-40 md:h-48 lg:h-52 col-span-2 lg:col-span-1" />
      )}
      <div className="col-span-2 flex justify-end gap-0.5">
        <AlbumInfoDialog editAlbum={album}>
          <button type="button" className="hidden" ref={albumInfoDialogTrigger} />
        </AlbumInfoDialog>
        <Button className="rounded-r-xs rounded-l-full grow sm:grow-0">
          <ImageIcon />
          Lấy ảnh khách đã tym
        </Button>
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <Button className="rounded-l-xs rounded-r-full">
              <ChevronDown />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <Link href={`/albums/${album.code}`}>
              <DropdownMenuItem className="cursor-pointer">
                <BookImage className="text-inherit" />
                Quản lý album
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem className="cursor-pointer" onClick={() => albumInfoDialogTrigger.current?.click()}>
              <Icon icon={editRoundedIcon} className="text-inherit" />
              Sửa thông tin album
            </DropdownMenuItem>
            <DropdownMenuItem variant="destructive" className="cursor-pointer" onClick={onDelete} disabled={!onDelete}>
              <Trash2 className="text-inherit" />
              Xoá album
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Card>
  )
}

export default AlbumCard
