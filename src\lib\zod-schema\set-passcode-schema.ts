import { z } from 'zod/v4'

export const setPasscodeSchema = z
  .object({
    passcode: z.string().min(6, { message: '<PERSON><PERSON><PERSON> khẩu phải dài hơn 6 ký tự và không có dấu cách' }),
    confirmPasscode: z.string().min(1, { message: '<PERSON><PERSON><PERSON> khẩu xác nhận không được để trống' }),
  })
  .refine((data) => data.passcode === data.confirmPasscode, {
    message: '<PERSON>ật khẩu không khớp',
    path: ['confirmPasscode'],
  })

export type SetPasscodeSchemaType = z.infer<typeof setPasscodeSchema>

export const changePasscodeSchema = setPasscodeSchema
  .extend({
    oldPasscode: z.string().min(1, { message: '<PERSON><PERSON><PERSON> khẩu cũ không được để trống' }),
  })
  .refine((data) => data.passcode === data.confirmPasscode, {
    message: '<PERSON><PERSON><PERSON> khẩu không khớp',
    path: ['confirmPasscode'],
  })

export type ChangePasscodeSchemaType = z.infer<typeof changePasscodeSchema>
